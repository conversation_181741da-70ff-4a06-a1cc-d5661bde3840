import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import '../api/api_provider.dart';
import '../models/task_item.dart';

/// 任务桥接类
/// 提供原生端调用Flutter端任务API的桥接功能
class TaskBridge {
  static const MethodChannel _channel = MethodChannel('com.xunhe.aishoucang/task_bridge');
  static bool _isInitialized = false;

  /// 初始化任务桥接
  static Future<void> init() async {
    if (_isInitialized) return;

    _channel.setMethodCallHandler(_handleMethodCall);
    _isInitialized = true;
    debugPrint('TaskBridge 初始化完成');
  }

  /// 处理原生端的方法调用
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    try {
      debugPrint('TaskBridge 收到方法调用: ${call.method}');
      debugPrint('TaskBridge 参数: ${call.arguments}');

      switch (call.method) {
        case 'getTaskList':
          return await _getTaskList(call.arguments);
        default:
          throw PlatformException(
            code: 'UNIMPLEMENTED',
            message: '未实现的方法: ${call.method}',
          );
      }
    } catch (e, stackTrace) {
      debugPrint('TaskBridge 方法调用失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 获取任务列表
  static Future<Map<String, dynamic>> _getTaskList(Map<dynamic, dynamic> arguments) async {
    try {
      final page = arguments['page'] as int? ?? 1;
      final pageSize = arguments['pageSize'] as int? ?? 10;
      final taskType = arguments['taskType'] as int?;
      final platform = arguments['platform'] as String?;
      final status = arguments['status'] as int?;

      debugPrint('获取任务列表: page=$page, pageSize=$pageSize, taskType=$taskType, platform=$platform, status=$status');

      // 调用API获取任务列表
      final apiProvider = ApiProvider();
      final response = await apiProvider.taskApi.getTaskList(
        page: page,
        pageSize: pageSize,
        taskType: taskType,
        platform: platform,
        status: status,
      );

      debugPrint('任务列表API响应: $response');

      // 转换为原生端需要的格式
      final taskListResponse = TaskListResponse.fromJson(response);
      final result = <String, dynamic>{
        'success': true,
        'data': {
          'tasks': taskListResponse.tasks.map((task) => {
            'id': task.id,
            'title': task.title,
            'description': '${task.taskTypeName} - ${task.platformDisplayName}',
            'status': _convertStatusToNative(task.status),
            'progress': task.status == 2 ? 100 : (task.status == 1 ? 0 : 0),
            'createdAt': task.createTime.millisecondsSinceEpoch,
            'updatedAt': task.updateTime.millisecondsSinceEpoch,
            'type': _convertTaskTypeToNative(task.taskType),
            'platform': task.platform,
            'platformName': task.platformDisplayName,
            'url': task.url,
          }).toList(),
          'total': taskListResponse.total,
          'page': taskListResponse.page,
          'pageSize': taskListResponse.pageSize,
          'hasMore': taskListResponse.hasMore,
        },
      };

      debugPrint('返回给原生端的数据: $result');
      return result;
    } catch (e, stackTrace) {
      debugPrint('获取任务列表失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 将API状态转换为原生端状态
  static String _convertStatusToNative(int apiStatus) {
    switch (apiStatus) {
      case 1:
        return 'PENDING'; // 新创建 -> 等待中
      case 2:
        return 'COMPLETED'; // 已完成
      case 3:
        return 'FAILED'; // 失败
      default:
        return 'PENDING';
    }
  }

  /// 将API任务类型转换为原生端任务类型
  static String _convertTaskTypeToNative(int apiTaskType) {
    switch (apiTaskType) {
      case 2:
        return 'VIDEO_DOWNLOAD'; // 下载视频
      default:
        return 'UNKNOWN';
    }
  }

  /// 主动调用原生端方法（如果需要）
  static Future<void> notifyTaskUpdate(String taskId, int status) async {
    try {
      await _channel.invokeMethod('onTaskUpdate', {
        'taskId': taskId,
        'status': _convertStatusToNative(status),
      });
    } catch (e) {
      debugPrint('通知任务更新失败: $e');
    }
  }
}
