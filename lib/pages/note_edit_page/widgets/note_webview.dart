import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/note_edit_model.dart';

/// 笔记WebView组件
class NoteWebview extends StatefulWidget {
  /// 笔记数据模型
  final NoteEditModel noteModel;

  /// 页面开始加载回调
  final VoidCallback? onPageStarted;

  /// 页面加载完成回调
  final VoidCallback? onPageFinished;

  /// 页面加载错误回调
  final Function(String error)? onPageError;

  /// JavaScript执行回调
  final Function(String script)? onExecuteScript;

  /// 内容变化回调（用于保存历史记录）
  final Function(String htmlContent)? onContentChanged;

  const NoteWebview({
    super.key,
    required this.noteModel,
    this.onPageStarted,
    this.onPageFinished,
    this.onPageError,
    this.onExecuteScript,
    this.onContentChanged,
  });

  @override
  State<NoteWebview> createState() => NoteWebviewState();
}

class NoteWebviewState extends State<NoteWebview> {
  late final WebViewController _webViewController;

  // JavaScript执行结果的Completer
  Completer<bool>? _jsExecutionCompleter;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  /// 执行JavaScript脚本
  Future<bool> executeScript(String script) async {
    try {
      print('准备执行JavaScript脚本: $script');

      // 创建新的Completer等待JavaScript执行结果
      _jsExecutionCompleter = Completer<bool>();

      // 生成唯一的执行ID，用于区分不同的执行请求
      final executionId = DateTime.now().millisecondsSinceEpoch.toString();

      // 简化的JavaScript包装脚本
      final wrappedScript = '''
try {
  $script;
  if (window.JSExecutionHelper) {
    window.JSExecutionHelper.reportResult(true, 'SUCCESS', '$executionId');
  }
} catch (error) {
  console.error('JavaScript执行错误:', error);
  if (window.JSExecutionHelper) {
    window.JSExecutionHelper.reportResult(false, error.name + ': ' + error.message, '$executionId');
  }
}
      ''';

      // 执行JavaScript
      await _webViewController.runJavaScript(wrappedScript);

      // 等待JavaScript执行结果回调，设置超时时间
      final result = await _jsExecutionCompleter!.future.timeout(
        Duration(seconds: 5),
        onTimeout: () {
          print('JavaScript执行超时，执行ID: $executionId');
          return false;
        },
      );

      print('JavaScript执行完成，执行ID: $executionId, 结果: $result');
      return result;
    } catch (e) {
      print('执行JavaScript失败: $e');
      return false;
    } finally {
      _jsExecutionCompleter = null;
    }
  }

  /// 获取当前页面的最新DOM结构
  Future<String?> getLatestDom() async {
    try {
      print('准备获取最新DOM结构');

      // 只获取body内容，避免模板占位符和头部信息问题
      final result = await _webViewController.runJavaScriptReturningResult('''(function() {return document.documentElement.outerHTML;})();''');


      if (result != null) {
        final domString = result.toString();
        // 在这里就进行解码，避免存储转义后的内容
        final decodedDom = _decodeHtmlString(domString);

        return decodedDom;
      } else {
        print('获取DOM结构失败: 返回结果为空');
        return null;
      }
    } catch (e) {
      print('获取DOM结构失败: $e');
      return null;
    }
  }

  /// 解码HTML字符串，处理WebView返回的转义字符
  String _decodeHtmlString(String htmlString) {
    // 去除转义后的html内容中，第一个和最后一个双引号，"xxxx" -> xxxx
    String processedString = htmlString;

    // 检查字符串是否以双引号开头和结尾，如果是则去除
    if (processedString.length >= 2 &&
        processedString.startsWith('"') &&
        processedString.endsWith('"')) {
      processedString = processedString.substring(1, processedString.length - 1);
    }

    return processedString
        // 处理Unicode转义
        .replaceAll('\\u003C', '<')
        .replaceAll('\\u003E', '>')
        .replaceAll('\\u0026', '&')
        .replaceAll('\\u0022', '"')
        .replaceAll('\\u0027', "'")
        .replaceAll('\\u002F', '/')
        .replaceAll('\\u003D', '=')
        // 处理常规转义
        .replaceAll('\n', '')
        .replaceAll('\\n', '\n')
        .replaceAll('\\r', '\r')
        .replaceAll('\\t', '\t')
        .replaceAll('\\"', '"')
        .replaceAll("\\'", "'")
        .replaceAll('\\\\', '\\');
  }

  /// 获取当前滚动位置
  Future<Map<String, double>?> getCurrentScrollPosition() async {
    try {
      final result = await _webViewController.runJavaScriptReturningResult('''
        (function() {
          return {
            scrollTop: window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0,
            scrollLeft: window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0
          };
        })();
      ''');

      if (result != null) {
        final resultString = result.toString();
        print('获取滚动位置结果: $resultString');

        // 解析返回的JSON字符串
        final decoded = jsonDecode(resultString) as Map<String, dynamic>;
        return {
          'scrollTop': (decoded['scrollTop'] as num).toDouble(),
          'scrollLeft': (decoded['scrollLeft'] as num).toDouble(),
        };
      }
      return null;
    } catch (e) {
      print('获取滚动位置失败: $e');
      return null;
    }
  }

  /// 设置滚动位置（不使用动画）
  Future<bool> setScrollPosition(double scrollTop, double scrollLeft) async {
    try {
      final script = '''
        (function() {
          // 立即设置滚动位置，不使用动画
          window.scrollTo($scrollLeft, $scrollTop);
          // 确保设置生效
          document.documentElement.scrollTop = $scrollTop;
          document.documentElement.scrollLeft = $scrollLeft;
          document.body.scrollTop = $scrollTop;
          document.body.scrollLeft = $scrollLeft;
          return true;
        })();
      ''';

      await _webViewController.runJavaScript(script);
      print('设置滚动位置成功: scrollTop=$scrollTop, scrollLeft=$scrollLeft');
      return true;
    } catch (e) {
      print('设置滚动位置失败: $e');
      return false;
    }
  }

  /// 加载HTML内容到WebView
  Future<void> loadHtmlContent(String htmlContent) async {
    try {
      await _webViewController.loadHtmlString(htmlContent);
    } catch (e) {
      print('加载HTML内容失败: $e');
    }
  }

  /// 加载HTML内容并恢复滚动位置
  Future<void> loadHtmlContentWithScrollPosition(String htmlContent, Map<String, double>? scrollPosition) async {
    try {
      // 先加载HTML内容
      await _webViewController.loadHtmlString(htmlContent);

      // 如果有滚动位置信息，等待页面加载完成后恢复滚动位置
      if (scrollPosition != null) {
        // 等待一小段时间确保页面完全加载
        await Future.delayed(Duration(milliseconds: 100));

        // 恢复滚动位置
        await setScrollPosition(
          scrollPosition['scrollTop'] ?? 0,
          scrollPosition['scrollLeft'] ?? 0,
        );
      }
    } catch (e) {
      print('加载HTML内容并恢复滚动位置失败: $e');
    }
  }

  @override
  void didUpdateWidget(NoteWebview oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果笔记内容发生变化，重新加载
    if (oldWidget.noteModel.htmlContent != widget.noteModel.htmlContent) {
      _loadContent();
    }
  }

  /// 初始化WebView
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            widget.onPageStarted?.call();
          },
          onPageFinished: (String url) {
            // 页面加载完成后注入JavaScript Bridge
            _injectJavaScriptBridge();
            widget.onPageFinished?.call();
          },
          onWebResourceError: (WebResourceError error) {
            widget.onPageError?.call('加载失败: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterBridge',
        onMessageReceived: (JavaScriptMessage message) {
          _handleJavaScriptMessage(message);
        },
      );

    // 加载内容
    _loadContent();
  }

  /// 注入JavaScript Bridge
  Future<void> _injectJavaScriptBridge() async {
    try {
      // 从assets中加载JavaScript文件
      final bridgeScript = await rootBundle.loadString('assets/js/webview_bridge.js');
      await _webViewController.runJavaScript(bridgeScript);
      print('JavaScript Bridge注入成功');
    } catch (e) {
      print('注入JavaScript Bridge失败: $e');
    }
  }

  /// 处理JavaScript消息
  void _handleJavaScriptMessage(JavaScriptMessage message) {
    try {
      final data = jsonDecode(message.message) as Map<String, dynamic>;
      final type = data['type'] as String?;

      if (type == 'jsExecutionResult') {
        final success = data['success'] as bool? ?? false;
        final resultMessage = data['message'] as String? ?? '';
        final executionId = data['executionId'] as String? ?? 'unknown';

        print('收到JavaScript执行结果: success=$success, message=$resultMessage, executionId=$executionId');

        // 完成Completer
        if (_jsExecutionCompleter != null && !_jsExecutionCompleter!.isCompleted) {
          _jsExecutionCompleter!.complete(success);
        }
      } else if (type == 'contentEditable_changed') {
        final changeData = data['data'] as Map<String, dynamic>?;
        if (changeData != null) {
          _handleContentEditableChange(changeData);
        }
      }
    } catch (e) {
      print('处理JavaScript消息失败: $e');
      // 如果解析失败，认为执行失败
      if (_jsExecutionCompleter != null && !_jsExecutionCompleter!.isCompleted) {
        _jsExecutionCompleter!.complete(false);
      }
    }
  }

  /// 处理contentEditable内容变化
  void _handleContentEditableChange(Map<String, dynamic> data) {
    final changeType = data['type'] as String? ?? '';
    final html = data['html'] as String? ?? '';
    final tagName = data['tagName'] as String? ?? '';
    final timestamp = data['timestamp'] as int? ?? 0;

    print('ContentEditable事件: type=$changeType, tagName=$tagName');

    // 只处理聚焦时的历史记录保存
    if (changeType == 'focus_save_history') {
      _saveHistoryForManualEdit(html);
    }
  }

  /// 为手动编辑保存历史记录
  void _saveHistoryForManualEdit(String htmlContent) {
    try {
      if (htmlContent.isEmpty) {
        print('HTML内容为空，无法保存历史记录');
        return;
      }

      print('手动编辑：聚焦时保存历史记录，内容长度=${htmlContent.length}');

      // 通过回调通知父组件保存历史记录（父组件会进行去重判断）
      widget.onContentChanged?.call(htmlContent);

    } catch (e) {
      print('保存手动编辑历史记录失败: $e');
    }
  }

  /// 加载内容
  void _loadContent() {
    _webViewController.loadHtmlString(widget.noteModel.htmlContent);
  }

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(controller: _webViewController);
  }
}
