import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// 笔记编辑操作按钮组件
class NoteActionButtons extends StatelessWidget {
  /// AI编辑按钮点击回调
  final VoidCallback? onAiEdit;

  /// 撤销按钮点击回调
  final VoidCallback? onUndo;

  /// 预览按钮点击回调
  final VoidCallback? onPreview;

  /// 保存按钮点击回调
  final VoidCallback? onSave;

  /// 是否为预览模式
  final bool isPreviewMode;

  /// 是否显示撤销按钮
  final bool showUndoButton;

  const NoteActionButtons({
    super.key,
    this.onAiEdit,
    this.onUndo,
    this.onPreview,
    this.onSave,
    this.isPreviewMode = false,
    this.showUndoButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 16.w,
      top: MediaQuery.of(context).padding.top + 250.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 预览模式下只显示预览按钮
          if (isPreviewMode) ...[
            _buildActionButton(
              icon: Icons.visibility_off,
              label: '退出',
              onTap: onPreview,
            ),
          ] else ...[
            // 正常模式下显示所有按钮
            if (onAiEdit != null) ...[
              _buildActionButton(
                icon: Icons.smart_toy,
                label: 'AI编辑',
                onTap: onAiEdit,
              ),
              SizedBox(height: 8.h),
            ],
            // 撤销按钮（只有在有历史记录时才显示）
            if (showUndoButton && onUndo != null) ...[
              _buildActionButton(
                icon: Icons.undo,
                label: '撤销',
                onTap: onUndo,
              ),
              SizedBox(height: 8.h),
            ],
            _buildActionButton(
              icon: Icons.visibility,
              label: '预览',
              onTap: onPreview,
            ),
            if (onSave != null) ...[
              SizedBox(height: 8.h),
              _buildActionButton(
                icon: Icons.publish,
                label: '发布',
                onTap: onSave,
              ),
            ],
          ],
        ],
      ),
    );
  }

  /// 构建单个操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    VoidCallback? onTap,
    bool isPrimary = false,
  }) {
    final backgroundColor = isPrimary ? AppColors.primary : AppColors.surface;
    final iconColor = isPrimary ? Colors.white : AppColors.textSecondary;
    final labelColor = isPrimary ? Colors.white : AppColors.textSecondary;

    return Container(
      width: 48.w,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: AppColors.shadow.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 18.r,
                  color: iconColor,
                ),
                SizedBox(height: 3.h),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 9.sp,
                    color: labelColor,
                    fontWeight: isPrimary ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
