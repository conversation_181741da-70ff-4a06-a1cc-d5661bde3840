import 'package:aishoucang/api/api_client.dart';
import 'package:flutter/foundation.dart';

/// 任务API接口
class TaskApi {
  final ApiClient _apiClient;

  /// 构造函数
  TaskApi(this._apiClient);

  /// 创建任务
  ///
  /// [taskType] 任务类型：2=下载视频
  /// [title] 任务标题，长度不超过255个字符
  /// [platform] 平台信息，长度不超过50个字符，如：'bilibili'、'xiaohongshu'
  /// [url] 要提取的地址
  ///
  /// 返回创建成功的任务信息
  Future<Map<String, dynamic>> createTask({
    required int taskType,
    required String title,
    required String platform,
    required String url,
  }) async {
    try {
      debugPrint('创建任务: taskType=$taskType, title=$title, platform=$platform, url=$url');

      final data = <String, dynamic>{
        'task_type': taskType,
        'title': title,
        'platform': platform,
        'url': url,
      };

      debugPrint('请求数据: $data');

      final response = await _apiClient.post(
        '/task/create',
        data,
      );

      debugPrint('创建任务API响应: $response');
      return response['data'];
    } catch (e, stackTrace) {
      debugPrint('创建任务API调用失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 获取任务列表
  ///
  /// [page] 页码，从1开始
  /// [pageSize] 每页数量，范围1-100
  /// [taskType] 任务类型过滤，可选：2=下载视频
  /// [platform] 平台过滤，可选
  /// [status] 状态过滤，可选：1=新创建，2=已完成，3=失败
  ///
  /// 返回任务列表数据
  Future<Map<String, dynamic>> getTaskList({
    required int page,
    required int pageSize,
    int? taskType,
    String? platform,
    int? status,
  }) async {
    try {
      debugPrint('获取任务列表: page=$page, pageSize=$pageSize, taskType=$taskType, platform=$platform, status=$status');

      // 构建查询参数
      final queryParams = <String, dynamic>{
        'page': page,
        'page_size': pageSize,
      };

      if (taskType != null) {
        queryParams['task_type'] = taskType;
      }
      if (platform != null && platform.isNotEmpty) {
        queryParams['platform'] = platform;
      }
      if (status != null) {
        queryParams['status'] = status;
      }

      // 构建查询字符串
      final queryString = queryParams.entries
          .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');

      debugPrint('查询参数: $queryString');

      final response = await _apiClient.get('/task/list?$queryString');

      debugPrint('获取任务列表API响应: $response');
      return response['data'];
    } catch (e, stackTrace) {
      debugPrint('获取任务列表API调用失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }
}
