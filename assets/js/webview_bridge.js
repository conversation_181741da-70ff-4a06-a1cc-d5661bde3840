/**
 * WebView JavaScript Bridge
 * 提供Flutter与WebView之间的通信桥梁
 */

window.JSExecutionHelper = {
  /**
   * 报告JavaScript执行结果
   */
  reportResult: function(success, message, executionId) {
    if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
      FlutterBridge.postMessage(JSON.stringify({
        type: 'jsExecutionResult',
        success: success,
        message: message,
        executionId: executionId || 'unknown'
      }));
    } else {
      console.error('FlutterBridge channel not available');
    }
  },
  
  /**
   * 报告内容变化
   */
  reportContentChange: function(data) {
    if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
      FlutterBridge.postMessage(JSON.stringify({
        type: 'contentEditable_changed',
        data: data
      }));
    } else {
      console.error('FlutterBridge channel not available for content change');
    }
  },
  
  /**
   * 设置contentEditable监控
   */
  setupContentMonitor: function() {
    console.log('开始设置contentEditable监控');

    // 监听focus事件（获得焦点时保存编辑前状态）- 使用事件捕获
    document.addEventListener('focus', function(event) {
      console.log('检测到focus事件:', event.target.tagName, 'contentEditable:', event.target.contentEditable);

      if (event.target.contentEditable === 'true') {
        console.log('检测到contentEditable获得焦点，保存编辑前状态:', event.target.tagName);

        // 获取完整的HTML内容并保存到历史记录
        const currentHtml = document.documentElement.outerHTML;
        window.JSExecutionHelper.reportContentChange({
          type: 'focus_save_history',
          html: currentHtml,
          tagName: event.target.tagName || '',
          timestamp: Date.now()
        });
      }
    }, true); // 使用事件捕获

    // 也监听focusin事件作为备选
    document.addEventListener('focusin', function(event) {
      console.log('检测到focusin事件:', event.target.tagName, 'contentEditable:', event.target.contentEditable);

      if (event.target.contentEditable === 'true') {
        console.log('通过focusin检测到contentEditable获得焦点:', event.target.tagName);

        // 获取完整的HTML内容并保存到历史记录
        const currentHtml = document.documentElement.outerHTML;
        window.JSExecutionHelper.reportContentChange({
          type: 'focus_save_history',
          html: currentHtml,
          tagName: event.target.tagName || '',
          timestamp: Date.now()
        });
      }
    });

    // 添加点击事件监听作为备选方案
    document.addEventListener('click', function(event) {
      console.log('检测到click事件:', event.target.tagName, 'contentEditable:', event.target.contentEditable);

      if (event.target.contentEditable === 'true') {
        console.log('通过click检测到contentEditable被点击:', event.target.tagName);

        // 获取完整的HTML内容并保存到历史记录
        const currentHtml = document.documentElement.outerHTML;
        window.JSExecutionHelper.reportContentChange({
          type: 'focus_save_history',
          html: currentHtml,
          tagName: event.target.tagName || '',
          timestamp: Date.now()
        });
      }
    });

    console.log('contentEditable监控设置完成');
  }
};

console.log('JSExecutionHelper注入成功');

// 自动设置内容监控
if (window.JSExecutionHelper && window.JSExecutionHelper.setupContentMonitor) {
  window.JSExecutionHelper.setupContentMonitor();
}
