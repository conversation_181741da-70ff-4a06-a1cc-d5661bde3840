package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast
import android.accessibilityservice.AccessibilityService
import android.view.accessibility.AccessibilityNodeInfo
import android.graphics.Rect
import android.view.WindowManager
import com.bumptech.glide.Glide
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.OssManager
import com.xunhe.aishoucang.helpers.SourceToOssLinkHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.lib.WebViewHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.URL

/**
 * 抖音应用收藏项处理器
 */
object DouyinAppItemHandler {
    private const val TAG = "DouyinAppItemHandler"

    // 标记是否已经保存过，避免重复保存
    private var hasSaved = false

    // 缓存的作者名称
    private var cachedAuthorName: String? = null

    /**
     * 设置缓存的作者名称
     */
    fun setAuthorNameCache(authorName: String) {
        cachedAuthorName = authorName
        Log.d(TAG, "已设置缓存作者名称: $authorName")
    }

    /**
     * 获取缓存的作者名称
     */
    fun getCachedAuthorName(): String? {
        return cachedAuthorName
    }

    /**
     * 清除缓存的作者名称
     */
    fun clearAuthorNameCache() {
        cachedAuthorName = null
        Log.d(TAG, "已清除缓存作者名称")
    }

    /**
     * 书签数据类，用于整合所有书签相关信息
     */
    private data class BookmarkData(
        var authorName: String,
        var videoTitle: String,
        var coverUrl: String? = null,
        var schemeUrl: String? = null,
        val favoriteId: String?,
        var videoDesc: String? = null,  // 添加描述字段
        var authorAvatar: String? = null  // 添加作者头像字段
    ) {
        // 检查是否有足够的数据保存书签
        fun isReadyToSave(): Boolean {
            return favoriteId != null
        }

        // 检查是否有完整的数据保存书签（包括schemeUrl）
        fun hasCompleteData(): Boolean {
            return favoriteId != null && schemeUrl != null
        }
    }

    fun closeDouYinPanel(context: Context) {
        // 只有在分享面板显示时才尝试设置面板不可获取焦点
        if (SharePanelHelper.isSharePanelShowing) {
            try {
                // 设置面板不可获取焦点，使无障碍服务能操作后台应用
                val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                SharePanelHelper.setPanelCannotFocus(windowManager)
            } catch (e: Exception) {
                // 捕获可能的异常，避免影响后续操作
                Log.e(TAG, "设置面板不可获取焦点时出错: ${e.message}", e)
            }
        } else {
            Log.d(TAG, "分享面板未显示，跳过设置面板焦点")
        }

        // 尝试点击关闭抖音分享弹窗的按钮
        val service = AccessibilityHelper.AppAccessibilityService.getInstance()
        val updatedRoot = service?.rootInActiveWindow

        if (updatedRoot != null) {
            val closeButton = service.findNodesByViewId(
                updatedRoot,
                "com.ss.android.ugc.aweme:id/xm4"
            )

            if (closeButton.size >= 1) {
                Log.d(TAG, "找到抖音分享弹窗的关闭按钮，点击关闭")
                closeButton[0].performAction(AccessibilityNodeInfo.ACTION_CLICK)
            } else {
                Log.d(TAG, "未找到抖音分享弹窗的关闭按钮")
            }
        }
    }

    /**
     * 处理抖音应用的收藏项 - 主入口函数，负责场景分发
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "处理抖音应用收藏项:")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        // 关闭抖音分享面板
        closeDouYinPanel(context)

        // 根据当前内容类型分发到对应的处理函数
        val currentContentType = SharePanelHelper.getCurrentContentType()
        when (currentContentType) {
            ContentTypeConstants.DOUYIN_TYPE_GOODS -> {
                // 抖音商品页面
                Log.d(TAG, "检测到抖音商品内容类型，分发到商品处理函数")
                handleGoods(context, appPackage, clipboardContent, favoriteItem)
            }
            ContentTypeConstants.DOUYIN_TYPE_SHORT_VIDEO -> {
                // 抖音短视频页面
                Log.d(TAG, "检测到抖音短视频内容类型，分发到短视频处理函数")
                handleShortVideo(context, appPackage, clipboardContent, favoriteItem)
            }
            else -> {
                // 默认按短视频处理（向后兼容）
                Log.d(TAG, "未知内容类型 ($currentContentType)，默认按短视频处理")
                handleShortVideo(context, appPackage, clipboardContent, favoriteItem)
            }
        }
    }

    /**
     * 处理抖音短视频收藏
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    private fun handleShortVideo(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "处理抖音短视频收藏:")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        // 重置保存状态
        hasSaved = false

        // 从收藏夹中提取作者名称和分享链接，还有视频标题
        if (clipboardContent.isNullOrEmpty()) {
            Log.e(TAG, "剪贴板内容为空，无法提取信息")
            return
        }

        // 尝试从UI节点获取作者名称，如果获取失败则从剪贴板内容中提取
        val accessibilityService = AccessibilityHelper.AppAccessibilityService.getInstance()
        val authorName = if (cachedAuthorName != null) {
            // 优先使用缓存的作者名称
            Log.d(TAG, "使用缓存的作者名称: $cachedAuthorName")
            val name = cachedAuthorName!!
            // 使用后清除缓存
            clearAuthorNameCache()
            name
        } else if (accessibilityService != null) {
            extractAuthorNameFromUI(accessibilityService) ?: extractAuthorName(clipboardContent)
        } else {
            extractAuthorName(clipboardContent)
        }

        // 提取视频标题
        val videoTitle = extractVideoTitle(clipboardContent)

        // 提取视频描述
        val videoDesc = extractVideoDesc(clipboardContent)

        // 提取分享链接
        val shareUrl = extractShareUrl(clipboardContent)

        Log.d(TAG, "提取结果:")
        Log.d(TAG, "- 作者名称: $authorName")
        Log.d(TAG, "- 视频标题: $videoTitle")
        Log.d(TAG, "- 视频描述: $videoDesc")
        Log.d(TAG, "- 分享链接: $shareUrl")

        // 创建书签数据容器
        val bookmarkData = BookmarkData(
            authorName = authorName,
            videoTitle = videoTitle,
            favoriteId = favoriteItem?.id,
            videoDesc = videoDesc
        )

        // 使用WebViewHtmlExtractor执行业务特定的JavaScript
        if (shareUrl != "未找到链接") {
            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            Log.d(TAG, "使用WebViewHtmlExtractor.executeBusinessJavaScript处理抖音链接: $shareUrl")

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context,
                shareUrl,
                "DouyinVideo"
            ) { result, error ->

                if (error != null) {
                    // 显示错误消息
                    Log.e(TAG, "执行DouyinVideo.js失败: $error")
                    CustomToastHelper.showToast(context, "提取抖音信息失败: $error")

                    // 回退到原来的WebViewHelper方法
                    useWebViewHelper(
                        context,
                        shareUrl,
                        bookmarkData,
                        appPackage,
                        clipboardContent,
                        favoriteItem
                    )

                    return@executeBusinessJavaScript
                }

                if (result != null) {
                    try {
                        Log.d(TAG, "DouyinGoods.js执行结果: $result")

                        // 尝试解析JSON结果
                        val jsonResult = org.json.JSONObject(result)

                        // 提取所有需要的数据
                        var hasCoverImage = false
                        var coverImageUrl = ""

                        // 提取封面图片URL
                        if (jsonResult.has("coverImage")) {
                            coverImageUrl = jsonResult.getString("coverImage")
                            if (!coverImageUrl.isNullOrEmpty()) {
                                Log.d(TAG, "从JavaScript结果中提取到封面图片URL: $coverImageUrl")
                                hasCoverImage = true
                            }
                        }

                        // 提取作者头像URL
                        if (jsonResult.has("authorAvatar")) {
                            val authorAvatarUrl = jsonResult.getString("authorAvatar")
                            if (!authorAvatarUrl.isNullOrEmpty()) {
                                Log.d(TAG, "从JavaScript结果中提取到作者头像URL: $authorAvatarUrl")
                                bookmarkData.authorAvatar = authorAvatarUrl
                            }
                        }

                        // 提取作者名称
                        if (jsonResult.has("authorName")) {
                            val extractedAuthorName = jsonResult.getString("authorName")
                            if (!extractedAuthorName.isNullOrEmpty()) {
                                Log.d(TAG, "从JavaScript结果中提取到作者名称: $extractedAuthorName")
                                // 更新书签数据中的作者名称
                                if (bookmarkData.authorName == "未知作者") {
                                    bookmarkData.authorName = extractedAuthorName
                                }
                            }
                        }

                        // 提取视频标题
                        if (jsonResult.has("title")) {
                            val title = jsonResult.getString("title")
                            if (!title.isNullOrEmpty()) {
                                Log.d(TAG, "从JavaScript结果中提取到视频标题: $title")
                                // 更新书签数据中的视频标题
                                bookmarkData.videoTitle = title
                            }
                        }

                        // 检查是否包含拦截到的scheme URL
                        var hasSchemeUrl = false

                        if (result.contains("InterceptedSchemeUrl:")) {
                            val schemeUrlLine = result.lines().find { it.contains("InterceptedSchemeUrl:") }
                            if (schemeUrlLine != null) {
                                val schemeUrl = schemeUrlLine.substringAfter("InterceptedSchemeUrl:").trim()
                                Log.d(TAG, "从JavaScript结果中提取到scheme URL: $schemeUrl")

                                // 解析并保存scheme URL
                                val cleanSchemeUrl = parseDouyinScheme(schemeUrl)
                                bookmarkData.schemeUrl = cleanSchemeUrl
                                hasSchemeUrl = true
                            }
                        }

                        // 根据提取到的数据决定下一步操作
                        if (hasSchemeUrl) {
                            if (hasCoverImage) {
                                // 有scheme URL和封面图片，先转换封面图片再保存
                                convertCoverToOssLink(
                                    context,
                                    coverImageUrl,
                                    bookmarkData,
                                    appPackage,
                                    clipboardContent,
                                    favoriteItem
                                )
                            } else {
                                // 有scheme URL但没有封面图片，直接保存
                                if (bookmarkData.hasCompleteData()) {
                                    saveBookmarkWithData(
                                        context,
                                        bookmarkData,
                                        appPackage,
                                        clipboardContent,
                                        favoriteItem
                                    )
                                } else {
                                    Log.w(TAG, "缺少必要数据，无法保存书签")
                                    CustomToastHelper.showToast(context, "无法保存: 缺少收藏夹ID")
                                }
                            }
                        } else {
                            // 如果没有拦截到scheme URL，回退到原来的WebViewHelper方法
                            Log.d(TAG, "未从JavaScript结果中提取到scheme URL，回退到原来的方法")
                            useWebViewHelper(
                                context,
                                shareUrl,
                                bookmarkData,
                                appPackage,
                                clipboardContent,
                                favoriteItem
                            )
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解析DouyinGoods.js结果失败: ${e.message}", e)
                        CustomToastHelper.showToast(context, "获取抖音内容失败，请重试")

                        // 回退到原来的WebViewHelper方法
                        useWebViewHelper(
                            context,
                            shareUrl,
                            bookmarkData,
                            appPackage,
                            clipboardContent,
                            favoriteItem
                        )
                    }
                } else {
                    Log.e(TAG, "DouyinGoods.js执行结果为空")
                    CustomToastHelper.showToast(context, "获取抖音内容失败，请重试")

                    // 回退到原来的WebViewHelper方法
                    useWebViewHelper(
                        context,
                        shareUrl,
                        bookmarkData,
                        appPackage,
                        clipboardContent,
                        favoriteItem
                    )
                }
            }
        } else {
            Log.e(TAG, "无效的分享链接")
            CustomToastHelper.showToast(context, "无效的分享链接")
        }
    }

    /**
     * 处理抖音商品收藏
     */
    private fun handleGoods(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "开始处理抖音商品")
        Log.d(TAG, "抖音剪切板内容: $clipboardContent")

        if (clipboardContent.isNullOrEmpty()) {
            Log.e(TAG, "剪贴板内容为空，无法提取信息")
            CustomToastHelper.showToast(context, "剪贴板内容为空")
            return
        }

        val extractedLink = extractShareUrl(clipboardContent)

        if (extractedLink != "未找到链接") {
            Log.d(TAG, "开始处理抖音商品链接: $extractedLink")

            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context,
                extractedLink,
                "DouyinGoods"
            ) { result, error ->
                if (error != null) {
                    // 显示错误消息
                    CustomToastHelper.showToast(context, "获取商品信息失败，请重试")
                    Log.e(TAG, "提取商品信息失败: $error")
                    return@executeBusinessJavaScript
                }

                if (result != null) {
                    try {
                        // 解析JSON结果
                        val jsonObject = org.json.JSONObject(result)

                        // 检查是否有错误
                        if (jsonObject.has("error")) {
                            val errorMessage = jsonObject.getString("error")
                            Log.e(TAG, "提取商品数据时JavaScript报错: $errorMessage")

                            // 显示错误消息
                            CustomToastHelper.showToast(context, "获取商品信息失败，请重试")
                            return@executeBusinessJavaScript
                        }

                        // 提取商品数据
                        val title = jsonObject.optString("title", "未知商品")
                        val coverImage = jsonObject.optString("coverImage", "")
                        val shopName = jsonObject.optString("shopName", "未知店铺")
                        val shopAvatar = jsonObject.optString("shopAvatar", "")
                        val schemeURL = jsonObject.optString("schemeURL", extractedLink)

                        Log.d(TAG, "提取到商品数据:")
                        Log.d(TAG, "- 标题: $title")
                        Log.d(TAG, "- 封面: $coverImage")
                        Log.d(TAG, "- 店铺: $shopName")
                        Log.d(TAG, "- 头像: $shopAvatar")
                        Log.d(TAG, "- 链接: $schemeURL")

                        // 转换scheme URL协议为snssdk1128格式
                        val convertedSchemeURL = convertToDouyinScheme(schemeURL)

                        // 直接调用BookMark.addBookMark，不进行OSS上传
                        BookMark.addBookMark(
                            context = context,
                            influencer_name = shopName,
                            influencer_avatar = shopAvatar, // 直接使用原始头像URL
                            cover = coverImage, // 直接使用原始封面URL
                            title = title,
                            desc = "", // 商品描述设置为空字符串
                            parent_id = favoriteItem?.id ?: "",
                            scheme_url = convertedSchemeURL,
                            callback = { success: Boolean, errorMessage: String? ->
                                if (success) {
                                    CustomToastHelper.showToast(context, "收藏成功")
                                } else {
                                    CustomToastHelper.showToast(context, "收藏失败，请重试")
                                }
                            }
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "解析商品数据失败: ${e.message}", e)

                        // 显示错误消息
                        CustomToastHelper.showToast(context, "获取商品信息失败，请重试")
                    }
                } else {
                    // 显示错误消息
                    CustomToastHelper.showToast(context, "获取商品信息失败，请重试")
                    Log.e(TAG, "提取商品信息失败: 结果为空")
                }
            }
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到抖音链接")
            CustomToastHelper.showToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 使用原来的WebViewHelper方法提取头像和封面图片URL
     */
    private fun useWebViewHelper(
        context: Context,
        shareUrl: String,
        bookmarkData: BookmarkData,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "回退到使用WebViewHelper提取头像和封面图片URL: $shareUrl")

        WebViewHelper.getInstance(context).extractVideoUrl(
            url = shareUrl,
            timeout = 30000, // 30秒超时
            filters = mapOf(
                "cover" to { url, contentType ->
                    contentType.lowercase().contains("image") && (url.toString().lowercase()
                        .contains("biz_tag=aweme_video") || url.toString().lowercase()
                        .contains("biz_tag=aweme_images"))
                },
                "avatar" to { url, contentType ->
                    contentType.lowercase().contains("image") && url.toString().lowercase()
                        .contains("avatar") // 匹配头像图片
                },
                "video" to { url, contentType ->
                    contentType.lowercase().contains("video")
                }
            ),
            callback = { success: Boolean, result: Map<String, List<String>>?, errorMessage: String? ->
                if (success && result != null) {
                    // 处理拦截到的原生跳转协议
                    result["scheme"]?.let { schemes ->
                        Log.d(TAG, "拦截到的原生跳转协议（共 ${schemes.size} 个）:")
                        schemes.forEachIndexed { index, url ->
                            val cleanSchemeUrl = parseDouyinScheme(url)
                            Log.d(TAG, "原生协议[$index]: $cleanSchemeUrl")

                            // 保存捕获到的第一个原生协议URL（通常只有一个）
                            if (bookmarkData.schemeUrl == null) {
                                bookmarkData.schemeUrl = cleanSchemeUrl
                            }
                        }
                    }

                    // 处理作者头像
                    result["avatar"]?.firstOrNull()?.let { avatarUrl ->
                        Log.d(TAG, "找到作者头像: $avatarUrl")
                        bookmarkData.authorAvatar = avatarUrl
                    }

                    // 处理封面图片
                    if (!result["cover"].isNullOrEmpty()) {
                        val coverUrl = result["cover"]!!.first()
                        Log.d(TAG, "使用第一个封面图片: $coverUrl")

                        // 转换封面为OSS链接
                        convertCoverToOssLink(
                            context,
                            coverUrl,
                            bookmarkData,
                            appPackage,
                            clipboardContent,
                            favoriteItem
                        )
                    } else {
                        Log.d(TAG, "未找到封面图片，尝试直接保存")
                        // 如果没有封面但有必要的数据，处理作者头像并保存
                        processAuthorAvatarAndSaveBookmark(
                            context,
                            bookmarkData,
                            appPackage,
                            clipboardContent,
                            favoriteItem
                        )
                    }

                    // 记录捕获到的视频资源
                    result["video"]?.let { videos ->
                        Log.d(TAG, "匹配到 ${videos.size} 个视频资源")
                    }
                } else {
                    Log.e(TAG, "过滤失败，错误信息: $errorMessage")
                    CustomToastHelper.showToast(context, "获取视频信息失败，请重试")
                }
            }
        )
    }

    /**
     * 处理封面图片并继续保存流程
     */
    private fun convertCoverToOssLink(
        context: Context,
        coverUrl: String,
        bookmarkData: BookmarkData,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        // 直接使用原始封面URL，不上传到OSS
        CoroutineScope(Dispatchers.Main).launch {
            Log.d(TAG, "使用原始封面URL: $coverUrl")
            // 更新书签数据中的封面URL
            bookmarkData.coverUrl = coverUrl

            // 继续处理作者头像并保存书签
            processAuthorAvatarAndSaveBookmark(
                context,
                bookmarkData,
                appPackage,
                clipboardContent,
                favoriteItem
            )
        }
    }

    /**
     * 处理作者头像并保存书签
     */
    private fun processAuthorAvatarAndSaveBookmark(
        context: Context,
        bookmarkData: BookmarkData,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        // 检查是否有足够的数据保存书签
        if (!bookmarkData.hasCompleteData()) {
            Log.w(TAG, "缺少必要数据，无法保存书签")
            if (bookmarkData.schemeUrl == null) {
                CustomToastHelper.showToast(context, "无法保存: 未能获取视频链接")
            } else {
                CustomToastHelper.showToast(context, "无法保存: 缺少收藏夹ID")
            }
            return
        }

        // 直接保存书签，不上传头像到OSS
        CoroutineScope(Dispatchers.Main).launch {
            Log.d(TAG, "直接使用原始头像URL: ${bookmarkData.authorAvatar}")

            // 最终保存书签
            saveBookmarkWithData(
                context,
                bookmarkData,
                appPackage,
                clipboardContent,
                favoriteItem
            )
        }
    }

    /**
     * 使用书签数据保存书签到收藏夹（确保只调用一次）
     */
    private fun saveBookmarkWithData(
        context: Context,
        bookmarkData: BookmarkData,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        // 确保只保存一次
        if (hasSaved) {
            Log.d(TAG, "书签已保存，跳过重复保存")
            return
        }

        // 检查必要数据
        if (!bookmarkData.isReadyToSave()) {
            Log.w(TAG, "缺少必要数据，无法保存书签")
            return
        }

        // 检查schemeUrl是否为空
        if (bookmarkData.schemeUrl == null) {
            Log.e(TAG, "schemeUrl为空，无法保存书签")
            CustomToastHelper.showToast(context, "无法保存: 未能获取视频链接")
            return
        }

        // 标记为已保存
        hasSaved = true

        // 获取schemeUrl的本地副本，避免智能转换问题
        val schemeUrl = bookmarkData.schemeUrl
        if (schemeUrl != null) {
            // 调用保存方法
            saveBookmark(
                context = context,
                authorName = bookmarkData.authorName,
                coverUrl = bookmarkData.coverUrl,
                videoTitle = bookmarkData.videoTitle,
                schemeUrl = schemeUrl, // 使用本地副本
                favoriteId = bookmarkData.favoriteId!!, // favoriteId在isReadyToSave()中已检查
                appPackage = appPackage,
                clipboardContent = clipboardContent,
                favoriteItem = favoriteItem,
                videoDesc = bookmarkData.videoDesc,
                authorAvatar = bookmarkData.authorAvatar
            )
        } else {
            // 这种情况不应该发生，因为我们已经在前面检查过了
            Log.e(TAG, "schemeUrl为空，无法保存书签")
            CustomToastHelper.showToast(context, "无法保存: 未能获取视频链接")
        }
    }

    /**
     * 保存书签到收藏夹
     */
    private fun saveBookmark(
        context: Context,
        authorName: String,
        coverUrl: String?,
        videoTitle: String,
        schemeUrl: String, // 已在调用前检查非空
        favoriteId: String,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?,
        videoDesc: String? = null,
        authorAvatar: String? = null
    ) {
        Log.d(TAG, "开始保存书签到收藏夹: $favoriteId")
        Log.d(TAG, "- 作者: $authorName")
        Log.d(TAG, "- 标题: $videoTitle")
        Log.d(TAG, "- 封面: $coverUrl")
        Log.d(TAG, "- 原生链接: $schemeUrl")
        Log.d(TAG, "- 描述: $videoDesc")
        Log.d(TAG, "- 作者头像: $authorAvatar")

        // 调用BookMark API保存书签
        BookMark.addBookMark(
            context = context,
            influencer_name = authorName,
            influencer_avatar = authorAvatar,
            cover = coverUrl,
            title = videoTitle,
            desc = videoDesc,
            parent_id = favoriteId,
            scheme_url = schemeUrl,
            callback = { success, errorMessage ->
                if (success) {
                    Log.d(TAG, "保存书签成功")
                    CustomToastHelper.showToast(context, "收藏成功")
                } else {
                    Log.e(TAG, "保存书签失败: $errorMessage")
                    CustomToastHelper.showToast(context, "收藏失败，请重试")
                }
            }
        )
    }

    /**
     * 解析抖音原生协议
     */
    private fun parseDouyinScheme(schemeUrl: String): String {
        try {
            val cleanUrl = schemeUrl.split("?")[0]
            return cleanUrl
        } catch (e: Exception) {
            Log.e(TAG, "解析抖音原生协议出错: ${e.message}")
            return schemeUrl // 出错时返回原URL
        }
    }

    /**
     * 将抖音商品的scheme URL协议转换为snssdk1128格式
     *
     * @param schemeUrl 原始的scheme URL
     * @return 转换后的scheme URL
     */
    private fun convertToDouyinScheme(schemeUrl: String): String {
        try {
            // 如果已经是snssdk1128协议，直接返回
            if (schemeUrl.startsWith("snssdk1128://")) {
                return schemeUrl
            }

            // 如果是sslocal协议，转换为snssdk1128协议
            if (schemeUrl.startsWith("sslocal://")) {
                val convertedUrl = schemeUrl.replace("sslocal://", "snssdk1128://")
                Log.d(TAG, "转换scheme URL: $schemeUrl -> $convertedUrl")
                return convertedUrl
            }

            // 其他情况直接返回原URL
            return schemeUrl
        } catch (e: Exception) {
            Log.e(TAG, "转换抖音scheme URL时出错: ${e.message}")
            return schemeUrl // 出错时返回原URL
        }
    }

    /**
     * 从抖音分享内容中提取作者名称
     */
    private fun extractAuthorName(content: String): String {
        // 使用通用正则表达式匹配"【xxx的[任何类型]作品】"中的xxx
        val pattern = "【(.+?)的(?:.+?)?作品】".toRegex()
        val matchResult = pattern.find(content)

        return matchResult?.groupValues?.get(1) ?: "未知作者"
    }

    /**
     * 从抖音分享内容中提取视频标题
     */
    private fun extractVideoTitle(content: String): String {
        // 检查是否含有常规格式的作品标记
        val pattern = "【.+?的(?:.+?)?作品】".toRegex()
        val matchResult = pattern.find(content)

        if (matchResult != null) {
            // 常规格式：【xxx的作品】后面是标题
            val startIndex = matchResult.range.last + 1

            // 查找链接开始位置
            val endIndex = content.indexOf("http", startIndex)
            val hashtagIndex = content.indexOf("#", startIndex)

            // 确定实际结束位置（链接或#号，取最先出现的）
            val actualEndIndex = when {
                endIndex > 0 && hashtagIndex > 0 -> minOf(endIndex, hashtagIndex)
                endIndex > 0 -> endIndex
                hashtagIndex > 0 -> hashtagIndex
                else -> content.length
            }

            // 提取并清理标题
            val rawTitle = if (actualEndIndex == content.length) {
                content.substring(startIndex)
            } else {
                content.substring(startIndex, actualEndIndex)
            }

            // 清理特殊字符和格式
            return rawTitle.trim()
                .replace(Regex("^[,，.。:：;；\\s]*看看\\s*"), "") // 移除"看看"
                .trim()
        } else {
            // 无作者格式：第一个#号前的内容是标题
            val hashtagIndex = content.indexOf("#")
            if (hashtagIndex > 0) {
                return content.substring(0, hashtagIndex).trim()
            }

            // 如果没有#号，查找链接
            val httpIndex = content.indexOf("http")
            if (httpIndex > 0) {
                return content.substring(0, httpIndex).trim()
            }

            // 如果没有链接，返回所有内容
            return content.trim()
        }
    }

    /**
     * 从抖音分享内容中提取视频描述
     * 描述通常是第一个#号和http链接之间的内容
     */
    private fun extractVideoDesc(content: String): String? {
        val hashtagIndex = content.indexOf("#")
        if (hashtagIndex < 0) return null

        val httpIndex = content.indexOf("http", hashtagIndex)
        if (httpIndex < 0) return null

        val desc = content.substring(hashtagIndex, httpIndex).trim()
        return if (desc.isEmpty()) null else desc
    }

    /**
     * 从抖音分享内容中提取分享链接
     */
    private fun extractShareUrl(content: String): String {
        // 匹配从http开始到第一个空格为止的内容
        val httpStartIndex = content.indexOf("http")
        if (httpStartIndex == -1) return "未找到链接"

        // 从http开始位置往后查找第一个空格
        val spaceIndex = content.indexOf(" ", httpStartIndex)

        // 如果找到空格，则截取从http到空格之间的内容；如果没有空格，则截取到字符串结尾
        val shareUrl = if (spaceIndex != -1) {
            content.substring(httpStartIndex, spaceIndex)
        } else {
            content.substring(httpStartIndex)
        }

        Log.d(TAG, "提取的原始链接: $shareUrl")

        // 对提取的链接进行清理，去除可能的结尾标点符号
        val cleanedUrl =
            shareUrl.trim().trimEnd('.', ',', '。', '，', '!', '！', '?', '？', ':', '：', ';', '；')
        Log.d(TAG, "清理后的链接: $cleanedUrl")

        return cleanedUrl
    }

    /**
     * 从UI节点中提取作者名称
     * 获取所有com.ss.android.ugc.aweme:id/user_avatar节点，选择屏幕可见范围内的节点，取它的desc作为作者名称
     *
     * @param service 无障碍服务实例
     * @return 获取到的作者名称，如果获取失败则返回null
     */
    fun extractAuthorNameFromUI(service: AccessibilityService): String? {
        try {
            val rootNode = service.rootInActiveWindow ?: return null
            Log.d(TAG, "获取到rootNode")

            // 查找所有用户头像节点
            val avatarNodes =
                rootNode.findAccessibilityNodeInfosByViewId("com.ss.android.ugc.aweme:id/user_avatar")
            if (avatarNodes.isEmpty()) {
                Log.d(TAG, "未找到用户头像节点")
                return null
            }

            Log.d(TAG, "找到 ${avatarNodes.size} 个用户头像节点")

            // 获取屏幕尺寸
            val displayMetrics = service.resources.displayMetrics
            val screenHeight = displayMetrics.heightPixels
            val screenWidth = displayMetrics.widthPixels

            // 筛选屏幕可见范围内的节点
            val visibleAvatarNodes = avatarNodes.filter { node ->
                val rect = Rect()
                node.getBoundsInScreen(rect)

                // 检查节点是否在屏幕可见范围内
                rect.left >= 0 && rect.top >= 0 && rect.right <= screenWidth && rect.bottom <= screenHeight && node.isVisibleToUser
            }

            if (visibleAvatarNodes.isEmpty()) {
                Log.d(TAG, "未找到屏幕可见范围内的用户头像节点")
                return null
            }

            // 遍历可见节点，优先获取有描述信息的节点
            for (node in visibleAvatarNodes) {
                val contentDesc = node.contentDescription?.toString()
                if (!contentDesc.isNullOrEmpty()) {
                    Log.d(TAG, "从UI节点获取到作者名称: $contentDesc")
                    return contentDesc
                }
            }

            Log.d(TAG, "所有可见的用户头像节点都没有描述信息")
            return null
        } catch (e: Exception) {
            Log.e(TAG, "从UI节点提取作者名称时出错: ${e.message}", e)
            return null
        }
    }
}