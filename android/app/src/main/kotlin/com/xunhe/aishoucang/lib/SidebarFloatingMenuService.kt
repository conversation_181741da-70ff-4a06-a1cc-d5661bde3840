package com.xunhe.aishoucang.lib


import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager


import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.CreateNoteHelper
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor
import com.xunhe.aishoucang.helpers.UserAgentGenerator
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.lib.FFmpegHelper
import org.json.JSONObject

class SidebarFloatingMenuService : Service() {
    companion object {
        private const val TAG = "SidebarFloatingMenuService"
        var lastPosition: Pair<Int, Int>? = null
    }

    private var windowManager: WindowManager? = null
    private var floatingMenuView: View? = null
    private var params: WindowManager.LayoutParams? = null
    private var isClosing = false // 添加关闭状态标记，防止重复操作

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "侧边栏悬浮菜单服务创建")
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "侧边栏悬浮菜单服务启动")

        val positionX = intent?.getIntExtra("position_x", Int.MIN_VALUE)
        val positionY = intent?.getIntExtra("position_y", Int.MIN_VALUE)

        createFloatingMenu(
            if (positionX == Int.MIN_VALUE) null else positionX,
            if (positionY == Int.MIN_VALUE) null else positionY
        )

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "侧边栏悬浮菜单服务销毁")
        removeFloatingMenu()
        // 通知Helper更新状态
        SidebarFloatingMenuHelper.getInstance(this).updateShowingState(false)
    }

    private fun createFloatingMenu(positionX: Int? = null, positionY: Int? = null) {
        try {
            if (floatingMenuView != null) {
                Log.d(TAG, "悬浮菜单已存在，先移除")
                removeFloatingMenu()
            }

            val inflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
            floatingMenuView = inflater.inflate(R.layout.sidebar_floating_menu_layout, null)

            // 设置点击事件
            setupClickListeners()

            // 设置触摸事件（点击空白区域关闭菜单）
            floatingMenuView?.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        // 如果点击的是菜单外部，关闭菜单
                        hideFloatingMenu()
                        true
                    }
                    else -> false
                }
            }

            // 获取屏幕尺寸
            val screenWidth = getScreenWidth()
            val screenHeight = getScreenHeight()
            Log.d(TAG, "屏幕尺寸: ${screenWidth}x${screenHeight}")

            // 设置布局参数
            params = WindowManager.LayoutParams().apply {
                // 设置宽度为屏幕宽度的60%
                width = (screenWidth * 0.6).toInt()
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                format = PixelFormat.TRANSLUCENT
                // 设置gravity为左上角，使用绝对坐标
                gravity = Gravity.TOP or Gravity.LEFT
            }

            // 设置初始位置 - 屏幕中央显示
            val menuWidth = (screenWidth * 0.6).toInt()
            val defaultX = positionX ?: (screenWidth - menuWidth) / 2 // 水平居中
            val defaultY = positionY ?: (screenHeight - 400) / 2 // 垂直居中（减去菜单高度的估算值）

            params?.x = defaultX
            params?.y = defaultY

            Log.d(TAG, "创建悬浮菜单，位置: x=${params?.x}, y=${params?.y}")

            windowManager?.addView(floatingMenuView, params)

            // 更新最后位置
            lastPosition = Pair(params?.x ?: 0, params?.y ?: 0)

            Log.d(TAG, "悬浮菜单创建成功")

        } catch (e: Exception) {
            Log.e(TAG, "创建悬浮菜单失败", e)
        }
    }

    private fun setupClickListeners() {
        // 关闭按钮
        floatingMenuView?.findViewById<View>(R.id.close_button)?.setOnClickListener {
            Log.d(TAG, "点击关闭按钮")
            hideFloatingMenu()
        }

        // 录屏功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_record_screen)?.setOnClickListener {
            Log.d(TAG, "点击录屏按钮")
            CustomToastHelper.showShortToast(this, "录屏功能")
            hideFloatingMenu()
        }

        // 截屏功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_screenshot)?.setOnClickListener {
            Log.d(TAG, "点击截屏按钮")
            CustomToastHelper.showShortToast(this, "截屏功能")
            hideFloatingMenu()
        }

        // 投屏功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_cast_screen)?.setOnClickListener {
            Log.d(TAG, "点击投屏按钮")
            CustomToastHelper.showShortToast(this, "投屏功能")
            hideFloatingMenu()
        }

        // 息屏听剧功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_screen_off_listening)?.setOnClickListener {
            Log.d(TAG, "点击息屏听剧按钮")
            CustomToastHelper.showShortToast(this, "息屏听剧功能")
            hideFloatingMenu()
        }

        // 识别BGM功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_recognize_bgm)?.setOnClickListener {
            Log.d(TAG, "点击识别BGM按钮")
            CustomToastHelper.showShortToast(this, "识别BGM功能")
            hideFloatingMenu()
        }

        // 实时字幕功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_real_time_subtitle)?.setOnClickListener {
            Log.d(TAG, "点击实时字幕按钮")
            CustomToastHelper.showShortToast(this, "实时字幕功能")
            hideFloatingMenu()
        }

        // 创建笔记功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_create_note)?.setOnClickListener {
            Log.d(TAG, "点击创建笔记按钮")

            // 先隐藏悬浮菜单
            hideFloatingMenu()

            // 执行创建笔记操作逻辑
            CreateNoteHelper.handleCreateNote(this) { success, error ->
                if (success) {
                    Log.d(TAG, "创建笔记操作成功")
                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "已执行创建笔记操作")
                } else {
                    Log.w(TAG, "创建笔记操作失败: $error")
                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, error ?: "创建笔记操作失败")
                }
            }
        }



        // 下载视频功能
        floatingMenuView?.findViewById<View>(R.id.menu_item_download_video)?.setOnClickListener {
            Log.d(TAG, "点击下载视频按钮")

            // 先隐藏悬浮菜单
            hideFloatingMenu()

            // 执行下载视频操作
            handleDownloadVideoFromFloatingMenu()
        }
    }

    private fun hideFloatingMenu() {
        try {
            // 防止重复关闭操作
            if (isClosing) {
                Log.d(TAG, "正在关闭中，忽略重复操作")
                return
            }
            isClosing = true

            Log.d(TAG, "开始隐藏悬浮菜单")
            finishClosing()
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮菜单失败", e)
            finishClosing()
        }
    }



    /**
     * 完成关闭操作的统一方法
     */
    private fun finishClosing() {
        try {
            Log.d(TAG, "执行最终关闭操作")
            removeFloatingMenu()
            stopSelf()
        } catch (e: Exception) {
            Log.e(TAG, "最终关闭操作失败", e)
        }
    }

    private fun removeFloatingMenu() {
        try {
            floatingMenuView?.let { view ->
                windowManager?.removeView(view)
                floatingMenuView = null
                Log.d(TAG, "悬浮菜单已移除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "移除悬浮菜单失败", e)
        } finally {
            // 重置关闭状态标记
            isClosing = false
        }
    }

    private fun getScreenWidth(): Int {
        return resources.displayMetrics.widthPixels
    }

    private fun getScreenHeight(): Int {
        return resources.displayMetrics.heightPixels
    }

    /**
     * 处理悬浮菜单中的下载视频功能
     */
    private fun handleDownloadVideoFromFloatingMenu() {
        try {
            // 获取剪贴板内容
            val clipboardHelper = ClipboardHelper.getInstance(this)
            val clipText = clipboardHelper.getClipboardText()

            if (clipText.isNullOrEmpty()) {
                CustomToastHelper.showShortToast(this, "剪贴板内容为空")
                return
            }

            // 提取链接
            val extractedLinks = extractLinksFromText(clipText)
            if (extractedLinks.isEmpty()) {
                CustomToastHelper.showShortToast(this, "剪贴板中未找到有效链接")
                return
            }

            // 显示开始下载提示
            CustomToastHelper.showShortToast(this, "开始提取视频...")

            // 使用第一个链接进行视频提取
            val firstUrl = extractedLinks.first()
            extractVideoFromUrlForFloatingMenuDownload(firstUrl)

        } catch (e: Exception) {
            Log.e(TAG, "下载视频时发生异常", e)
            CustomToastHelper.showShortToast(this, "下载视频时发生异常: ${e.message}")
        }
    }

    /**
     * 从文本中提取HTTP/HTTPS链接
     */
    private fun extractLinksFromText(text: String): List<String> {
        val links = mutableListOf<String>()
        try {
            // 使用正则表达式匹配HTTP/HTTPS链接
            val urlPattern = Regex("https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+")
            val matches = urlPattern.findAll(text)

            for (match in matches) {
                val link = match.value
                if (!links.contains(link)) {
                    links.add(link)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取链接时发生错误", e)
        }
        return links
    }

    /**
     * 从URL提取视频资源用于悬浮菜单下载
     */
    private fun extractVideoFromUrlForFloatingMenuDownload(url: String) {
        Log.d(TAG, "开始从URL提取视频资源用于下载: $url")

        // 使用WebViewHtmlExtractor执行extractVideo.js脚本
        WebViewHtmlExtractor.executeBusinessJavaScript(
            context = this,
            url = url,
            businessName = "extractVideo"
        ) { result, error ->
            if (error != null) {
                Log.e(TAG, "视频提取失败 - URL: $url, 错误: $error")
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "视频提取失败: $error")
                }
                return@executeBusinessJavaScript
            } else if (result != null) {
                Log.d(TAG, "视频提取成功，结果: $result")

                try {
                    // 解析JSON结果
                    val jsonResult = JSONObject(result)
                    val success = jsonResult.optBoolean("success", false)

                    if (success) {
                        val videosArray = jsonResult.optJSONArray("videos")
                        if (videosArray != null && videosArray.length() > 0) {
                            // 获取第一个视频的URL
                            val firstVideo = videosArray.getJSONObject(0)
                            val videoSrc = firstVideo.optString("src", "")
                            val videoCurrentSrc = firstVideo.optString("currentSrc", "")

                            Log.d(TAG, "解析视频信息 - src: $videoSrc, currentSrc: $videoCurrentSrc")

                            // 优先使用currentSrc，如果没有或为null则使用src
                            val videoUrl = when {
                                videoCurrentSrc.isNotEmpty() && videoCurrentSrc != "null" -> videoCurrentSrc
                                videoSrc.isNotEmpty() && videoSrc != "null" -> videoSrc
                                else -> ""
                            }

                            if (videoUrl.isNotEmpty()) {
                                Log.d(TAG, "使用提取的视频URL进行下载: $videoUrl")
                                downloadVideoToGalleryFromFloatingMenu(videoUrl)
                            } else {
                                android.os.Handler(android.os.Looper.getMainLooper()).post {
                                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "未能成功提取视频链接，请稍后再试")
                                }
                            }
                        } else {
                            android.os.Handler(android.os.Looper.getMainLooper()).post {
                                CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "未能成功提取视频链接，请稍后再试")
                            }
                        }
                    } else {
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "未能成功提取视频链接，请稍后再试")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析视频提取结果时出错", e)
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "解析视频出错，请稍后再试")
                    }
                }
            } else {
                Log.d(TAG, "未从URL提取到任何视频资源，提示用户失败")
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "剪切板内容解析异常，请稍后再试")
                }
            }
        }
    }

    /**
     * 从悬浮菜单下载视频到相册
     */
    private fun downloadVideoToGalleryFromFloatingMenu(videoUrl: String) {
        Log.d(TAG, "开始下载视频到相册: $videoUrl")

        android.os.Handler(android.os.Looper.getMainLooper()).post {
            CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "开始下载视频...")
        }

        val ffmpegHelper = FFmpegHelper.getInstance(this)
        val finalUserAgent = UserAgentGenerator.generateUserAgentForUrl(videoUrl)
        val finalReferer = UserAgentGenerator.getRefererForUrl(videoUrl)

        ffmpegHelper.downloadVideo(
            url = videoUrl,
            userAgent = finalUserAgent,
            referer = finalReferer,
            outputPath = null, // 使用默认路径
            saveToGallery = true // 保存到相册
        ) { success, outputFilePath, errorMessage ->
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                if (success) {
                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "视频下载成功，已保存到相册")
                    Log.d(TAG, "视频下载成功: $outputFilePath")

                    // 下载成功后清空剪切板
                    try {
                        val clipboardHelper = ClipboardHelper.getInstance(this@SidebarFloatingMenuService)
                        clipboardHelper.setClipboardText("")
                        Log.d(TAG, "视频下载成功，已清空剪切板")
                    } catch (e: Exception) {
                        Log.e(TAG, "清空剪切板时发生异常", e)
                    }
                } else {
                    CustomToastHelper.showShortToast(this@SidebarFloatingMenuService, "视频下载失败: $errorMessage")
                    Log.e(TAG, "视频下载失败: $errorMessage")
                }
            }
        }
    }

}
