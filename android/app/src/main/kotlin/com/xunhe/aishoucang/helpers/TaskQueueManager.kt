package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.adapters.TaskQueueAdapter
import com.xunhe.aishoucang.api.TaskApi
import com.xunhe.aishoucang.models.TaskQueueItem
import com.xunhe.aishoucang.models.TaskStatus
import com.xunhe.aishoucang.models.TaskType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

/**
 * 任务队列管理器
 * 负责管理任务队列的显示和数据操作
 */
class TaskQueueManager(private val context: Context) {

    companion object {
        private const val TAG = "TaskQueueManager"
    }

    private var taskQueueAdapter: TaskQueueAdapter? = null
    private val tasks = mutableListOf<TaskQueueItem>()
    private var taskApi: TaskApi? = null
    private var rootView: View? = null
    private var swipeRefreshLayout: SwipeRefreshLayout? = null
    private var recyclerView: RecyclerView? = null
    private var currentPage = 1
    private val pageSize = 10
    private var isLoading = false
    private var hasMoreData = true

    /**
     * 初始化任务队列RecyclerView
     */
    fun setupTaskQueueRecyclerView(rootView: View, onItemClick: (TaskQueueItem) -> Unit) {
        this.recyclerView = rootView.findViewById(R.id.task_queue_recycler_view)
        this.swipeRefreshLayout = rootView.findViewById(R.id.task_queue_swipe_refresh)

        // 设置线性布局管理器
        val layoutManager = LinearLayoutManager(context)
        recyclerView?.layoutManager = layoutManager

        // 设置适配器 - 传递一个新的空列表，避免引用问题
        taskQueueAdapter = TaskQueueAdapter(context, mutableListOf(), onItemClick)
        recyclerView?.adapter = taskQueueAdapter

        // 保存根视图引用，用于控制状态显示
        this.rootView = rootView

        // 设置下拉刷新
        setupSwipeRefresh()

        // 设置上拉加载
        setupLoadMore()

        // 初始化TaskApi
        initTaskApi()

        // 显示加载状态并加载任务数据
        showLoadingState()
        loadTasks(refresh = true)

        Log.d(TAG, "任务队列RecyclerView初始化完成")
    }

    /**
     * 设置标签切换逻辑
     */
    fun setupTabSwitching(rootView: View) {
        val tabAiToolsContainer: View = rootView.findViewById(R.id.tab_ai_tools_container)
        val tabTaskQueueContainer: View = rootView.findViewById(R.id.tab_task_queue_container)
        val tabAiTools: TextView = rootView.findViewById(R.id.tab_ai_tools)
        val tabTaskQueue: TextView = rootView.findViewById(R.id.tab_task_queue)
        val tabAiToolsUnderline: View = rootView.findViewById(R.id.tab_ai_tools_underline)
        val tabTaskQueueUnderline: View = rootView.findViewById(R.id.tab_task_queue_underline)
        val aiToolsContent: View = rootView.findViewById(R.id.ai_tools_content)
        val taskQueueContent: View = rootView.findViewById(R.id.task_queue_content)
        val bottomButtonsContainer: View = rootView.findViewById(R.id.bottom_buttons_container)

        // AI工具箱标签点击
        tabAiToolsContainer.setOnClickListener {
            switchToAiTools(tabAiTools, tabTaskQueue, tabAiToolsUnderline, tabTaskQueueUnderline, aiToolsContent, taskQueueContent, bottomButtonsContainer)
        }

        // 任务队列标签点击
        tabTaskQueueContainer.setOnClickListener {
            switchToTaskQueue(tabAiTools, tabTaskQueue, tabAiToolsUnderline, tabTaskQueueUnderline, aiToolsContent, taskQueueContent, bottomButtonsContainer)
        }
    }

    /**
     * 切换到AI工具箱
     */
    private fun switchToAiTools(
        tabAiTools: TextView,
        tabTaskQueue: TextView,
        tabAiToolsUnderline: View,
        tabTaskQueueUnderline: View,
        aiToolsContent: View,
        taskQueueContent: View,
        bottomButtonsContainer: View
    ) {
        // 更新标签样式
        tabAiTools.setTextColor(0xFFFFFFFF.toInt())
        tabAiTools.textSize = 14f
        tabAiTools.paint.isFakeBoldText = true
        tabTaskQueue.setTextColor(0xFFAAAAAA.toInt())
        tabTaskQueue.textSize = 14f
        tabTaskQueue.paint.isFakeBoldText = false

        // 更新下划线显示
        tabAiToolsUnderline.visibility = View.VISIBLE
        tabTaskQueueUnderline.visibility = View.GONE

        // 切换内容显示
        aiToolsContent.visibility = View.VISIBLE
        taskQueueContent.visibility = View.GONE

        // 显示底部按钮容器
        bottomButtonsContainer.visibility = View.VISIBLE

        Log.d(TAG, "切换到AI工具箱")
    }

    /**
     * 切换到任务队列
     */
    private fun switchToTaskQueue(
        tabAiTools: TextView,
        tabTaskQueue: TextView,
        tabAiToolsUnderline: View,
        tabTaskQueueUnderline: View,
        aiToolsContent: View,
        taskQueueContent: View,
        bottomButtonsContainer: View
    ) {
        // 更新标签样式
        tabTaskQueue.setTextColor(0xFFFFFFFF.toInt())
        tabTaskQueue.textSize = 14f
        tabTaskQueue.paint.isFakeBoldText = true
        tabAiTools.setTextColor(0xFFAAAAAA.toInt())
        tabAiTools.textSize = 14f
        tabAiTools.paint.isFakeBoldText = false

        // 更新下划线显示
        tabTaskQueueUnderline.visibility = View.VISIBLE
        tabAiToolsUnderline.visibility = View.GONE

        // 切换内容显示
        taskQueueContent.visibility = View.VISIBLE
        aiToolsContent.visibility = View.GONE

        // 隐藏底部按钮容器
        bottomButtonsContainer.visibility = View.GONE

        Log.d(TAG, "切换到任务队列")

        // 切换到任务队列时刷新数据
        showLoadingState()
        refreshTasks()
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        swipeRefreshLayout?.apply {
            // 设置刷新颜色
            setColorSchemeColors(
                android.graphics.Color.parseColor("#3D7AF5"),
                android.graphics.Color.parseColor("#FF6B6B"),
                android.graphics.Color.parseColor("#4ECDC4")
            )

            // 设置刷新监听
            setOnRefreshListener {
                Log.d(TAG, "用户触发下拉刷新")
                refreshTasks()
            }
        }
    }

    /**
     * 设置上拉加载
     */
    private fun setupLoadMore() {
        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
                if (layoutManager != null && dy > 0) { // 向下滚动
                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val pastVisibleItems = layoutManager.findFirstVisibleItemPosition()

                    // 当滚动到接近底部时触发加载更多
                    if (!isLoading && hasMoreData &&
                        (visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                        Log.d(TAG, "触发上拉加载更多")
                        loadMoreTasks()
                    }
                }
            }
        })
    }

    /**
     * 初始化TaskApi
     */
    private fun initTaskApi() {
        try {
            taskApi = TaskApi.getInstance(context)
            Log.d(TAG, "TaskApi初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "初始化TaskApi失败: ${e.message}")
        }
    }

    /**
     * 加载任务数据
     */
    private fun loadTasks(refresh: Boolean = false, loadMore: Boolean = false) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求")
            return
        }

        if (loadMore && !hasMoreData) {
            Log.d(TAG, "没有更多数据，跳过加载")
            return
        }

        isLoading = true

        if (refresh) {
            currentPage = 1
            hasMoreData = true
        } else if (loadMore) {
            currentPage++
            // 显示加载更多指示器
            taskQueueAdapter?.showLoadingMore()
        }

        Log.d(TAG, "开始加载任务数据，页码: $currentPage")

        // 直接调用原生TaskApi
        taskApi?.getTaskList(
            page = currentPage,
            pageSize = pageSize,
            taskType = null,
            platform = null,
            status = null
        ) { success, result, error ->
            // 在主线程处理结果
            CoroutineScope(Dispatchers.Main).launch {
                isLoading = false
                // 停止下拉刷新动画
                swipeRefreshLayout?.isRefreshing = false
                // 隐藏加载更多指示器
                taskQueueAdapter?.hideLoadingMore()

                if (success && result != null) {
                    handleNativeApiResult(result, refresh)
                } else {
                    Log.e(TAG, "加载任务数据失败: $error")
                    // 如果API调用失败，使用模拟数据作为后备
                    generateMockTasks()
                    taskQueueAdapter?.updateItems(tasks)
                    // 更新UI状态
                    updateUIState()
                }
            }
        }
    }

    /**
     * 处理原生API结果
     */
    private fun handleNativeApiResult(result: String, refresh: Boolean) {
        try {
            Log.d(TAG, "处理原生API结果: $result")

            val jsonResponse = JSONObject(result)
            val tasksArray = jsonResponse.optJSONArray("tasks") ?: JSONArray()
            val total = jsonResponse.optInt("total", 0)
            val page = jsonResponse.optInt("page", currentPage)
            val pageSize = jsonResponse.optInt("page_size", this.pageSize)

            Log.d(TAG, "解析API响应: 任务数组长度=${tasksArray.length()}, 总数=$total, 页码=$page, 页大小=$pageSize")

            // 转换JSON数据为TaskQueueItem
            val newTasks = mutableListOf<TaskQueueItem>()
            Log.d(TAG, "开始转换任务，任务数组长度: ${tasksArray.length()}")

            for (i in 0 until tasksArray.length()) {
                val taskJson = tasksArray.getJSONObject(i)
                val taskItem = convertJsonTaskToTaskQueueItem(taskJson)
                newTasks.add(taskItem)
                Log.d(TAG, "添加任务到newTasks: ${taskItem.title}, newTasks大小: ${newTasks.size}")
            }

            Log.d(TAG, "转换完成，newTasks大小: ${newTasks.size}")

            if (refresh) {
                Log.d(TAG, "刷新模式，清空现有任务")
                tasks.clear()
            }

            Log.d(TAG, "添加新任务前，tasks大小: ${tasks.size}")
            tasks.addAll(newTasks)
            Log.d(TAG, "添加新任务后，tasks大小: ${tasks.size}")

            // 根据其他地方的实现，hasMore的计算逻辑是：当前数据量 < 总数
            hasMoreData = tasks.size < total
            Log.d(TAG, "计算hasMore: 当前数据量=${tasks.size}, 总数=$total, hasMore=$hasMoreData")

            Log.d(TAG, "更新适配器前，tasks大小: ${tasks.size}")
            taskQueueAdapter?.updateItems(tasks)
            Log.d(TAG, "更新适配器后")

            // 更新UI状态
            updateUIState()

            Log.d(TAG, "任务数据加载完成，当前任务数: ${tasks.size}, 总数: $total, 还有更多: $hasMoreData")
        } catch (e: Exception) {
            Log.e(TAG, "处理原生API结果失败: ${e.message}")
            // 如果解析失败，使用模拟数据作为后备
            generateMockTasks()
            taskQueueAdapter?.updateItems(tasks)
            // 更新UI状态
            updateUIState()
        }
    }

    /**
     * 将JSON任务数据转换为TaskQueueItem
     */
    private fun convertJsonTaskToTaskQueueItem(taskJson: JSONObject): TaskQueueItem {
        val id = taskJson.optString("id", "")
        val title = taskJson.optString("title", "")
        val description = taskJson.optString("description", title) // 如果没有description，使用title
        val statusInt = taskJson.optInt("status", 1)
        val progress = taskJson.optInt("progress", 0)

        // 处理时间字段，API返回的是create_time和update_time
        val createTimeStr = taskJson.optString("create_time", "")
        val updateTimeStr = taskJson.optString("update_time", "")

        val createdAt = if (createTimeStr.isNotEmpty()) {
            try {
                // 解析ISO 8601格式的时间字符串
                val formatter = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", java.util.Locale.getDefault())
                formatter.timeZone = java.util.TimeZone.getTimeZone("UTC")
                formatter.parse(createTimeStr) ?: Date()
            } catch (e: Exception) {
                Log.w(TAG, "解析创建时间失败: $createTimeStr", e)
                Date()
            }
        } else {
            Date()
        }

        val updatedAt = if (updateTimeStr.isNotEmpty()) {
            try {
                val formatter = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", java.util.Locale.getDefault())
                formatter.timeZone = java.util.TimeZone.getTimeZone("UTC")
                formatter.parse(updateTimeStr) ?: Date()
            } catch (e: Exception) {
                Log.w(TAG, "解析更新时间失败: $updateTimeStr", e)
                Date()
            }
        } else {
            Date()
        }

        val typeInt = taskJson.optInt("task_type", 0)
        val platform = taskJson.optString("platform", "")
        val platformName = getPlatformName(platform)

        val status = when (statusInt) {
            1 -> TaskStatus.PENDING
            2 -> TaskStatus.COMPLETED
            3 -> TaskStatus.FAILED
            else -> TaskStatus.PENDING
        }

        val type = when (typeInt) {
            2 -> TaskType.VIDEO_DOWNLOAD
            else -> TaskType.UNKNOWN
        }

        Log.d(TAG, "转换任务: id=$id, title=$title, status=$status, type=$type, platform=$platformName")

        return TaskQueueItem(
            id = id,
            title = title,
            description = description,
            status = status,
            progress = progress,
            createdAt = createdAt,
            updatedAt = updatedAt,
            type = type,
            platform = platform,
            platformName = platformName
        )
    }

    /**
     * 获取平台显示名称
     */
    private fun getPlatformName(platform: String): String {
        return when (platform.lowercase()) {
            "bilibili" -> "B站"
            "douyin" -> "抖音"
            "xiaohongshu" -> "小红书"
            "wechat" -> "微信"
            "weibo" -> "微博"
            else -> platform
        }
    }

    /**
     * 生成模拟任务数据（后备方案）
     */
    private fun generateMockTasks() {
        val now = Date()
        val calendar = Calendar.getInstance()

        tasks.clear()

        // 添加不同状态的模拟任务
        tasks.add(
            TaskQueueItem(
                id = "task_1",
                title = "下载抖音视频",
                description = "正在下载《如何提高工作效率》视频...",
                status = TaskStatus.RUNNING,
                progress = 75,
                createdAt = now,
                updatedAt = now,
                type = TaskType.VIDEO_DOWNLOAD,
                platform = "douyin",
                platformName = "抖音"
            )
        )



        calendar.add(Calendar.MINUTE, -10)
        tasks.add(
            TaskQueueItem(
                id = "task_3",
                title = "创建学习笔记",
                description = "等待处理《Flutter开发指南》笔记创建",
                status = TaskStatus.PENDING,
                progress = 0,
                createdAt = calendar.time,
                updatedAt = calendar.time,
                type = TaskType.NOTE_CREATE,
                platform = "wechat",
                platformName = "微信"
            )
        )

        Log.d(TAG, "生成模拟任务数据完成，任务数量: ${tasks.size}")
    }

    /**
     * 添加新任务
     */
    fun addTask(task: TaskQueueItem) {
        tasks.add(0, task)
        taskQueueAdapter?.addItem(task)
        Log.d(TAG, "添加新任务: ${task.title}")
    }

    /**
     * 更新任务状态
     */
    fun updateTaskStatus(taskId: String, status: TaskStatus, progress: Int = 0) {
        val taskIndex = tasks.indexOfFirst { it.id == taskId }
        if (taskIndex != -1) {
            val updatedTask = tasks[taskIndex].copy(
                status = status,
                progress = progress,
                updatedAt = Date()
            )
            tasks[taskIndex] = updatedTask
            taskQueueAdapter?.updateItem(taskId, updatedTask)
            Log.d(TAG, "更新任务状态: $taskId -> $status")
        }
    }

    /**
     * 移除任务
     */
    fun removeTask(taskId: String) {
        tasks.removeAll { it.id == taskId }
        taskQueueAdapter?.removeItem(taskId)
        Log.d(TAG, "移除任务: $taskId")
    }

    /**
     * 刷新任务列表
     */
    fun refreshTasks() {
        Log.d(TAG, "刷新任务列表")
        loadTasks(refresh = true)
    }

    /**
     * 加载更多任务
     */
    fun loadMoreTasks() {
        Log.d(TAG, "加载更多任务")
        loadTasks(loadMore = true)
    }

    /**
     * 显示加载状态
     */
    private fun showLoadingState() {
        rootView?.let { root ->
            val recyclerView = root.findViewById<RecyclerView>(R.id.task_queue_recycler_view)
            val emptyState = root.findViewById<View>(R.id.task_queue_empty_state)
            val loadingState = root.findViewById<View>(R.id.task_queue_loading_state)

            recyclerView?.visibility = View.GONE
            emptyState?.visibility = View.GONE
            loadingState?.visibility = View.VISIBLE
        }
    }

    /**
     * 显示内容状态
     */
    private fun showContentState() {
        rootView?.let { root ->
            val recyclerView = root.findViewById<RecyclerView>(R.id.task_queue_recycler_view)
            val emptyState = root.findViewById<View>(R.id.task_queue_empty_state)
            val loadingState = root.findViewById<View>(R.id.task_queue_loading_state)

            recyclerView?.visibility = View.VISIBLE
            emptyState?.visibility = View.GONE
            loadingState?.visibility = View.GONE
        }
    }

    /**
     * 显示空状态
     */
    private fun showEmptyState() {
        rootView?.let { root ->
            val recyclerView = root.findViewById<RecyclerView>(R.id.task_queue_recycler_view)
            val emptyState = root.findViewById<View>(R.id.task_queue_empty_state)
            val loadingState = root.findViewById<View>(R.id.task_queue_loading_state)

            recyclerView?.visibility = View.GONE
            emptyState?.visibility = View.VISIBLE
            loadingState?.visibility = View.GONE
        }
    }

    /**
     * 更新UI状态
     */
    private fun updateUIState() {
        if (isLoading) {
            showLoadingState()
        } else if (tasks.isEmpty()) {
            showEmptyState()
        } else {
            showContentState()
        }
    }
}
