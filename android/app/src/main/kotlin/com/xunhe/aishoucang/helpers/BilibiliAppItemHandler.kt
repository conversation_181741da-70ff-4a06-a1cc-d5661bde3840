package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import android.graphics.Rect
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import org.json.JSONObject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * B站应用收藏项处理器
 */
object BilibiliAppItemHandler {
    private const val TAG = "BilibiliAppItemHandler"

    // 标记是否已经保存过，避免重复保存
    private var hasSaved = false

    // 标记是否正在处理请求
    private var isProcessing = false

    // 缓存的作者名称
    private var cachedAuthorName: String? = null

    /**
     * 设置缓存的作者名称
     */
    fun setAuthorNameCache(authorName: String) {
        cachedAuthorName = authorName
        Log.d(TAG, "已设置缓存作者名称: $authorName")
    }

    /**
     * 获取缓存的作者名称
     */
    fun getCachedAuthorName(): String? {
        return cachedAuthorName
    }

    /**
     * 清除缓存的作者名称
     */
    fun clearAuthorNameCache() {
        cachedAuthorName = null
        Log.d(TAG, "已清除缓存作者名称")
    }

    /**
     * 书签数据类，用于整合所有书签相关信息
     */
    data class BookmarkData(
        val videoTitle: String,
        var coverUrl: String? = null,
        var schemeUrl: String? = null,
        val favoriteId: String?,
        var authorName: String? = null,
        var authorAvatar: String? = null
    ) {
        // 检查是否有足够的数据保存书签
        fun isReadyToSave(): Boolean {
            return favoriteId != null
        }
    }

    /**
     * 从UI节点中提取作者名称
     * 获取tv.danmaku.bili:id/author_name节点的text作为作者名称
     *
     * @param service 无障碍服务实例
     * @return 获取到的作者名称，如果获取失败则返回null
     */
    fun extractAuthorNameFromUI(service: android.accessibilityservice.AccessibilityService): String? {
        try {
            val rootNode = service.rootInActiveWindow ?: return null
            Log.d(TAG, "获取到rootNode")

            // 查找作者名称节点
            val authorNodes: List<AccessibilityNodeInfo> = rootNode.findAccessibilityNodeInfosByViewId("tv.danmaku.bili:id/author_name")
            if (authorNodes.isEmpty()) {
                Log.d(TAG, "未找到作者名称节点")
                return null
            }

            Log.d(TAG, "找到 ${authorNodes.size} 个作者名称节点")

            // 获取屏幕尺寸
            val displayMetrics = service.resources.displayMetrics
            val screenHeight = displayMetrics.heightPixels
            val screenWidth = displayMetrics.widthPixels

            // 筛选屏幕可见范围内的节点
            val visibleAuthorNodes = authorNodes.filter { node ->
                val rect = Rect()
                node.getBoundsInScreen(rect)

                // 检查节点是否在屏幕可见范围内
                rect.left >= 0 && rect.top >= 0 && rect.right <= screenWidth && rect.bottom <= screenHeight && node.isVisibleToUser
            }

            if (visibleAuthorNodes.isEmpty()) {
                Log.d(TAG, "未找到屏幕可见范围内的作者名称节点")
                return null
            }

            // 遍历可见节点，获取文本内容
            for (node in visibleAuthorNodes) {
                val text = node.text?.toString()
                if (!text.isNullOrEmpty()) {
                    Log.d(TAG, "从UI节点获取到作者名称: $text")
                    return text
                }
            }

            Log.d(TAG, "所有可见的作者名称节点都没有文本内容")
            return null
        } catch (e: Exception) {
            Log.e(TAG, "从UI节点提取作者名称时出错: ${e.message}", e)
            return null
        }
    }

    /**
     * 处理B站应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "处理B站应用收藏项:")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        // 如果已经在处理请求，则跳过
        if (isProcessing) {
            Log.d(TAG, "已有请求正在处理中，跳过")
            return
        }

        // 标记为正在处理
        isProcessing = true

        // 尝试获取作者名称，优先使用缓存的作者名称
        val authorName = if (cachedAuthorName != null) {
            // 优先使用缓存的作者名称
            Log.d(TAG, "使用缓存的作者名称: $cachedAuthorName")
            val name = cachedAuthorName!!
            // 使用后清除缓存
            clearAuthorNameCache()
            name
        } else {
            // 如果没有缓存，使用默认名称
            "xxxx"
        }
        Log.d(TAG, "使用作者名称: $authorName")

        // 重置保存状态
        hasSaved = false

        // 从剪贴板内容中提取信息
        if (clipboardContent.isNullOrEmpty()) {
            Log.e(TAG, "剪贴板内容为空，无法提取信息")
            CustomToastHelper.showToast(context, "剪贴板内容为空")
            return
        }

        // 提取视频标题和分享链接
        val videoTitle = extractVideoTitle(clipboardContent)
        val shareUrl = extractShareUrl(clipboardContent)

        Log.d(TAG, "提取结果:")
        Log.d(TAG, "- 视频标题: $videoTitle")
        Log.d(TAG, "- 分享链接: $shareUrl")

        // 创建书签数据容器
        val bookmarkData = BookmarkData(
            videoTitle = videoTitle,
            favoriteId = favoriteItem?.id,
            authorName = authorName
        )

        // 使用WebViewHtmlExtractor执行业务特定的JavaScript
        if (shareUrl != "未找到链接") {
            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            Log.d(TAG, "开始使用WebViewHtmlExtractor执行BilibiliGoods.js")

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context,
                shareUrl,
                "BilibiliGoods"
            ) { result, error ->
                if (error != null) {

                    // 显示错误消息
                    Log.e(TAG, "执行BilibiliGoods.js失败: $error")
                    CustomToastHelper.showToast(context, "提取B站信息失败: $error")

                    // 显示错误提示
                    CustomToastHelper.showToast(context, "当前内容异常，无法收藏")

                    // 重置处理状态
                    isProcessing = false

                    return@executeBusinessJavaScript
                }

                // 处理JavaScript返回的结果
                if (result != null) {
                    try {
                        Log.d(TAG, "BilibiliGoods.js执行成功，返回结果: $result")

                        // 解析JSON结果
                        val jsonObject = JSONObject(result)

                        // 直接从JavaScript返回的URL中提取视频ID
                        try {
                            if (jsonObject.has("url")) {
                                val jsUrl = jsonObject.getString("url")
                                Log.d(TAG, "从JavaScript返回的URL中提取视频ID: $jsUrl")

                                val jsVideoId = extractVideoId(jsUrl)
                                if (jsVideoId != null) {
                                    val schemeUrl = "bilibili://video/$jsVideoId"
                                    Log.d(TAG, "从JavaScript返回的URL提取视频ID: $jsVideoId，构建scheme URL: $schemeUrl")
                                    bookmarkData.schemeUrl = schemeUrl
                                } else {
                                    Log.e(TAG, "无法从JavaScript返回的URL提取视频ID: $jsUrl")
                                }
                            } else {
                                Log.e(TAG, "JavaScript返回的结果中没有URL字段")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "从JavaScript返回的URL提取视频ID时出错: ${e.message}", e)
                        }

                        // 检查是否有错误
                        if (jsonObject.has("error")) {
                            val errorMessage = jsonObject.getString("error")
                            Log.e(TAG, "BilibiliGoods.js报错: $errorMessage")

                            // 如果已经构建了scheme URL，可以直接使用
                            if (bookmarkData.schemeUrl != null) {
                                Log.d(TAG, "虽然JavaScript报错，但已构建scheme URL，继续处理")

                                // 如果已经获取到必要数据，保存书签
                                if (bookmarkData.isReadyToSave()) {
                                    saveBookmarkWithData(
                                        context,
                                        bookmarkData,
                                        appPackage,
                                        clipboardContent,
                                        favoriteItem
                                    )
                                    return@executeBusinessJavaScript
                                }
                            }

                            // 显示错误提示
                            // 显示错误提示
                            CustomToastHelper.showToast(context, "获取B站内容失败，请重试")
                            // 重置处理状态
                            isProcessing = false

                            return@executeBusinessJavaScript
                        }

                        // 提取视频标题
                        if (jsonObject.has("title")) {
                            val title = jsonObject.getString("title")
                            Log.d(TAG, "提取到视频标题: $title")
                            // 更新bookmarkData对象
                            val newBookmarkData = BookmarkData(
                                videoTitle = title,
                                coverUrl = bookmarkData.coverUrl,
                                schemeUrl = bookmarkData.schemeUrl,
                                favoriteId = bookmarkData.favoriteId,
                                authorName = bookmarkData.authorName,
                                authorAvatar = bookmarkData.authorAvatar
                            )

                            // 提取其他信息
                            val coverImage = if (jsonObject.has("coverImage")) jsonObject.getString("coverImage") else null
                            val extractedAuthorName = if (jsonObject.has("authorName")) jsonObject.getString("authorName") else newBookmarkData.authorName
                            val authorAvatar = if (jsonObject.has("authorAvatar")) jsonObject.getString("authorAvatar") else null

                            if (coverImage != null) {
                                Log.d(TAG, "提取到封面图片: $coverImage")
                                newBookmarkData.coverUrl = coverImage
                            }

                            if (extractedAuthorName != newBookmarkData.authorName) {
                                Log.d(TAG, "提取到UP主名称: $extractedAuthorName")
                                newBookmarkData.authorName = extractedAuthorName
                            }

                            if (authorAvatar != null) {
                                Log.d(TAG, "提取到UP主头像: $authorAvatar")
                                newBookmarkData.authorAvatar = authorAvatar
                            }

                            // 如果已经构建了scheme URL，可以直接保存书签
                            if (newBookmarkData.schemeUrl != null) {
                                Log.d(TAG, "已构建scheme URL，直接保存书签")

                                // 如果已经获取到必要数据，保存书签
                                if (newBookmarkData.isReadyToSave()) {
                                    saveBookmarkWithData(
                                        context,
                                        newBookmarkData,
                                        appPackage,
                                        clipboardContent,
                                        favoriteItem
                                    )
                                    return@executeBusinessJavaScript
                                }
                            }

                            // 如果没有构建scheme URL，直接提示错误
                            Log.e(TAG, "未能构建scheme URL，无法收藏")
                            // 显示错误提示
                            CustomToastHelper.showToast(context, "当前内容异常，无法收藏")
                            // 重置处理状态
                            isProcessing = false
                            return@executeBusinessJavaScript
                        }

                        // 如果没有标题，使用原始标题并提取其他信息
                        // 提取所有信息并更新BookmarkData对象
                        val coverImage = if (jsonObject.has("coverImage")) jsonObject.getString("coverImage") else null
                        val extractedAuthorName = if (jsonObject.has("authorName")) jsonObject.getString("authorName") else bookmarkData.authorName
                        val authorAvatar = if (jsonObject.has("authorAvatar")) jsonObject.getString("authorAvatar") else null

                        if (coverImage != null) {
                            Log.d(TAG, "提取到封面图片: $coverImage")
                            bookmarkData.coverUrl = coverImage
                        }

                        if (extractedAuthorName != bookmarkData.authorName) {
                            Log.d(TAG, "提取到UP主名称: $extractedAuthorName")
                            bookmarkData.authorName = extractedAuthorName
                        }

                        if (authorAvatar != null) {
                            Log.d(TAG, "提取到UP主头像: $authorAvatar")
                            bookmarkData.authorAvatar = authorAvatar
                        }

                        // 如果已经构建了scheme URL，可以直接保存书签
                        if (bookmarkData.schemeUrl != null) {
                            Log.d(TAG, "已构建scheme URL，直接保存书签")

                            // 如果已经获取到必要数据，保存书签
                            if (bookmarkData.isReadyToSave()) {
                                saveBookmarkWithData(
                                    context,
                                    bookmarkData,
                                    appPackage,
                                    clipboardContent,
                                    favoriteItem
                                )
                                return@executeBusinessJavaScript
                            }
                        }

                        // 如果没有构建scheme URL，直接提示错误
                        Log.e(TAG, "未能构建scheme URL，无法收藏")
                        // 显示错误提示
                        CustomToastHelper.showToast(context, "获取B站内容失败，请重试")
                        // 重置处理状态
                        isProcessing = false
                    } catch (e: Exception) {
                        Log.e(TAG, "解析BilibiliGoods.js结果失败: ${e.message}", e)

                        // 显示错误提示
                        // 显示错误提示
                        CustomToastHelper.showToast(context, "获取B站内容失败，请重试")
                        // 重置处理状态
                        isProcessing = false
                    }
                } else {
                    Log.e(TAG, "BilibiliGoods.js执行结果为空")

                    // 显示错误提示
                    CustomToastHelper.showToast(context, "获取B站内容失败，请重试")
                    // 重置处理状态
                    isProcessing = false
                }
            }
        } else {
            Log.e(TAG, "无效的分享链接")
            CustomToastHelper.showToast(context, "无效的分享链接")
            // 重置处理状态
            isProcessing = false
        }
    }



    /**
     * 处理原生协议URL，去除复杂参数
     * 例如：bilibili://video/114329949116595?page=0&h5awaken=... 转换为 bilibili://video/114329949116595
     */
    private fun cleanSchemeUrl(schemeUrl: String): String {
        return try {
            // 分割URL，只取问号前的部分
            schemeUrl.split("?")[0]
        } catch (e: Exception) {
            Log.e(TAG, "清理scheme URL时出错: ${e.message}")
            schemeUrl // 出错时返回原URL
        }
    }

    /**
     * 保存书签数据
     */
    private fun saveBookmarkWithData(
        context: Context,
        bookmarkData: BookmarkData,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        // 确保只保存一次
        if (hasSaved) {
            Log.d(TAG, "书签已保存，跳过重复保存")
            return
        }

        // 检查必要数据
        if (!bookmarkData.isReadyToSave()) {
            Log.w(TAG, "缺少必要数据，无法保存书签")
            isProcessing = false
            return
        }

        // 检查schemeUrl是否为空
        if (bookmarkData.schemeUrl == null) {
            Log.e(TAG, "schemeUrl为空，无法保存书签")
            CustomToastHelper.showToast(context, "无法保存: 未能获取视频链接")
            isProcessing = false
            return
        }

        // 标记为已保存
        hasSaved = true

        // 清理scheme URL
        val cleanedSchemeUrl = cleanSchemeUrl(bookmarkData.schemeUrl!!)

        // 直接在主线程中调用保存方法，不进行OSS上传
        CoroutineScope(Dispatchers.Main).launch {
            // 调用保存方法
            saveBookmark(
                context = context,
                videoTitle = bookmarkData.videoTitle,
                coverUrl = bookmarkData.coverUrl, // 直接使用原始封面URL
                schemeUrl = cleanedSchemeUrl,
                favoriteId = bookmarkData.favoriteId!!,
                appPackage = appPackage,
                clipboardContent = clipboardContent,
                favoriteItem = favoriteItem,
                authorName = bookmarkData.authorName,
                authorAvatar = bookmarkData.authorAvatar // 直接使用原始头像URL
            )
        }
    }

    /**
     * 保存书签到收藏夹
     */
    private fun saveBookmark(
        context: Context,
        videoTitle: String,
        coverUrl: String?,
        schemeUrl: String,
        favoriteId: String,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?,
        authorName: String? = null,
        authorAvatar: String? = null
    ) {
        Log.d(TAG, "开始保存书签到收藏夹: $favoriteId")
        Log.d(TAG, "- 标题: $videoTitle")
        Log.d(TAG, "- 封面: $coverUrl")
        Log.d(TAG, "- 原生链接: $schemeUrl")
        Log.d(TAG, "- 作者: $authorName")
        Log.d(TAG, "- 头像: $authorAvatar")

        // 获取平台类型
        val platformType = SharePanelHelper.getCurrentPlatformType(schemeUrl)

        // 调用BookMark API保存书签
        BookMark.addBookMark(
            context = context,
            influencer_name = authorName ?: "未知作者", // 使用传入的作者名称，如果为空则使用"未知作者"
            influencer_avatar = authorAvatar,
            cover = coverUrl,
            title = videoTitle,
            desc = null,
            parent_id = favoriteId,
            scheme_url = schemeUrl,
            platform_type = platformType,
            callback = { success, errorMessage ->
                // 重置处理状态
                isProcessing = false

                if (success) {
                    Log.d(TAG, "保存书签成功")
                    CustomToastHelper.showToast(context, "收藏成功")
                } else {
                    Log.e(TAG, "保存书签失败: $errorMessage")
                    CustomToastHelper.showToast(context, "收藏失败，请重试")
                }
            }
        )
    }

    /**
     * 从B站分享内容中提取视频标题
     */
    private fun extractVideoTitle(content: String): String {
        // 匹配【标题-哔哩哔哩】格式
        val pattern = "【(.+?)-哔哩哔哩】".toRegex()
        val matchResult = pattern.find(content)

        return if (matchResult != null) {
            matchResult.groupValues[1]
        } else {
            // 如果没有匹配到标准格式，尝试提取第一个链接前的所有内容
            val httpIndex = content.indexOf("http")
            if (httpIndex > 0) {
                content.substring(0, httpIndex).trim()
            } else {
                content.trim()
            }
        }
    }

    /**
     * 从B站分享内容中提取分享链接
     */
    private fun extractShareUrl(content: String): String {
        // 匹配从http开始到第一个空格为止的内容
        val httpStartIndex = content.indexOf("http")
        if (httpStartIndex == -1) return "未找到链接"

        // 从http开始位置往后查找第一个空格
        val spaceIndex = content.indexOf(" ", httpStartIndex)

        // 如果找到空格，则截取从http到空格之间的内容；如果没有空格，则截取到字符串结尾
        val shareUrl = if (spaceIndex != -1) {
            content.substring(httpStartIndex, spaceIndex)
        } else {
            content.substring(httpStartIndex)
        }

        Log.d(TAG, "提取的原始链接: $shareUrl")

        // 对提取的链接进行清理，去除可能的结尾标点符号
        val cleanedUrl = shareUrl.trim().trimEnd('.', ',', '。', '，', '!', '！', '?', '？', ':', '：', ';', '；')
        Log.d(TAG, "清理后的链接: $cleanedUrl")

        return cleanedUrl
    }

    /**
     * 从B站分享链接中提取视频ID
     * 支持多种格式：
     * - https://b23.tv/xxxxx (短链接)
     * - https://www.bilibili.com/video/BVxxxxxx (BV号)
     * - https://www.bilibili.com/video/avxxxxxx (av号)
     * - https://m.bilibili.com/video/BVxxxxxx (移动版BV号)
     * - https://m.bilibili.com/video/BVxxxxxx?... (带参数的BV号)
     */
    private fun extractVideoId(url: String): String? {
        try {
            // 处理短链接 (b23.tv)
            if (url.contains("b23.tv")) {
                // 短链接需要先获取重定向后的URL，这里我们直接返回null
                // 因为我们无法在这里进行HTTP请求获取重定向URL
                // 但我们会在JavaScript返回的URL中尝试提取
                Log.d(TAG, "检测到短链接，无法直接提取视频ID: $url")
                return null
            }

            // 处理标准链接 - BV号 (包括带参数的情况)
            val bvPattern = "bilibili\\.com/video/(BV[0-9a-zA-Z]+)".toRegex()
            val bvMatch = bvPattern.find(url)
            if (bvMatch != null) {
                val bvid = bvMatch.groupValues[1]
                Log.d(TAG, "提取到BV号: $bvid")
                return bvid
            }

            // 处理av号链接
            val avPattern = "bilibili\\.com/video/av([0-9]+)".toRegex()
            val avMatch = avPattern.find(url)
            if (avMatch != null) {
                val avid = avMatch.groupValues[1]
                Log.d(TAG, "提取到av号: $avid")
                return avid
            }

            // 处理其他可能的格式 - 尝试匹配任何video/后面的ID部分
            val otherPattern = "video/([0-9a-zA-Z]+)".toRegex()
            val otherMatch = otherPattern.find(url)
            if (otherMatch != null) {
                val id = otherMatch.groupValues[1]
                // 检查是否包含问号，如果有则截取问号前的部分
                val cleanId = if (id.contains("?")) {
                    id.substring(0, id.indexOf("?"))
                } else {
                    id
                }
                Log.d(TAG, "提取到其他格式视频ID: $cleanId")
                return cleanId
            }

            Log.e(TAG, "无法从URL中提取视频ID: $url")
            return null
        } catch (e: Exception) {
            Log.e(TAG, "提取视频ID时出错: ${e.message}", e)
            return null
        }
    }
}