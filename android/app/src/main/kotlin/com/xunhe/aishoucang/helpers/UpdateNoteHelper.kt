package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.api.NoteApi
import com.xunhe.aishoucang.api.TaskApi
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.TaskStatusPoller
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.helpers.ClipboardLinkExtractor
import com.xunhe.aishoucang.helpers.UrlPlatformExtractor
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor
import com.xunhe.aishoucang.helpers.UserAgentGenerator
import com.xunhe.aishoucang.helpers.OssManager
import com.xunhe.aishoucang.lib.FFmpegHelper
import com.xunhe.aishoucang.lib.RequestHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.xunhe.aishoucang.helpers.ConfigHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * 更新笔记助手类
 * 负责处理更新笔记时的各个步骤：防抖检查、关闭面板、任务检查、内容提取、视频处理、任务创建、状态轮询等
 */
object UpdateNoteHelper {
    private const val TAG = "UpdateNoteHelper"
    private const val DEBOUNCE_INTERVAL = 1000L // 防抖间隔时间（毫秒）

    // 记录上次点击时间，用于防抖
    private var lastClickTime = 0L

    /**
     * 处理更新笔记点击事件
     * 包含防抖功能，先关闭收藏面板，然后执行后续逻辑
     *
     * @param context 上下文
     * @param noteId 要更新的笔记ID
     */
    fun handleUpdateNoteClick(context: Context, noteId: String) {
        Log.d(TAG, "更新笔记按钮被点击，笔记ID: $noteId")

        // 1. 防抖检查
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime < DEBOUNCE_INTERVAL) {
            Log.d(TAG, "点击过于频繁，忽略本次操作")
            return
        }
        lastClickTime = currentTime

        // 检查笔记ID是否有效
        if (noteId.isEmpty()) {
            CustomToastHelper.showToast(context, "笔记ID无效")
            return
        }

        // 保存要更新的笔记ID
        NoteApi.currentUpdateNoteId = noteId
        Log.d(TAG, "已保存要更新的笔记ID: $noteId")

        // 2. 关闭面板
        try {
            SharePanelHelper.hideSharePanelWithoutParam(false)
            Log.d(TAG, "成功关闭收藏面板")
        } catch (e: Exception) {
            Log.e(TAG, "关闭收藏面板失败", e)
        }

        // 3. 先检查会员状态
        checkMembershipAndProceed(context, noteId)
    }

    /**
     * 检查会员状态并继续处理
     *
     * @param context 上下文
     * @param noteId 笔记ID
     */
    private fun checkMembershipAndProceed(context: Context, noteId: String) {
        Log.d(TAG, "开始检查会员状态")

        // 检查用户是否已登录
        val currentUserId = SharedPreferencesHelper.getInstance(context).getUserId()
        if (currentUserId.isEmpty()) {
            Log.w(TAG, "用户未登录")
            CustomToastHelper.showToast(context, "请先登录")
            clearTemporaryData()
            return
        }

        // 调用AI使用量API检查会员状态
        checkAiUsage(context) { success, hasUsage, error ->
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                if (success) {
                    if (hasUsage) {
                        Log.d(TAG, "会员校验通过，继续检查任务状态")
                        // 会员校验通过，继续检查任务状态
                        checkTaskAndProceed(context, noteId)
                    } else {
                        Log.w(TAG, "用户没有剩余使用次数")
                        CustomToastHelper.showToast(context, "您的AI使用次数已用完，请升级会员")
                        clearTemporaryData()
                    }
                } else {
                    Log.e(TAG, "检查会员状态失败: $error")
                    CustomToastHelper.showToast(context, "$error")
                    clearTemporaryData()
                }
            }
        }
    }

    /**
     * 检查AI使用量
     *
     * @param context 上下文
     * @param callback 回调函数，参数为(是否成功, 是否有剩余次数, 错误信息)
     */
    private fun checkAiUsage(context: Context, callback: (Boolean, Boolean, String?) -> Unit) {
        try {
            val requestHelper = RequestHelper.getInstance(context)
            val apiBaseUrl = ConfigHelper.getString("api_base_url")
            val url = "$apiBaseUrl/ai-usage/get"

            Log.d(TAG, "请求AI使用量API: $url")

            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val result = requestHelper.get(url)

                    when (result) {
                        is RequestHelper.ApiResult.Success -> {
                            val jsonResult = JSONObject(result.data)
                            val code = jsonResult.optInt("code", -1)

                            if (code == 0) {
                                val data = jsonResult.optJSONObject("data")
                                val usageCount = data?.optInt("usage_count", 0) ?: 0

                                Log.d(TAG, "AI使用量查询成功，剩余次数: $usageCount")
                                callback(true, usageCount > 0, null)
                            } else {
                                val message = jsonResult.optString("message", "未知错误")
                                Log.e(TAG, "AI使用量查询失败: $message")
                                callback(false, false, message)
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            // 尝试解析错误响应体中的具体错误信息
                            val errorMessage = try {
                                if (!result.errorBody.isNullOrEmpty()) {
                                    val errorJson = JSONObject(result.errorBody)
                                    val errorCode = errorJson.optInt("code", -1)
                                    val errorMsg = errorJson.optString("message", "未知错误")

                                    Log.e(TAG, "AI使用量API返回错误: code=$errorCode, message=$errorMsg")

                                    // 根据错误码返回相应的用户友好提示
                                    when (errorCode) {
                                        408 -> "无权使用AI功能，请联系管理员"
                                        401 -> "登录已过期，请重新登录"
                                        403 -> "权限不足，请联系管理员"
                                        else -> errorMsg
                                    }
                                } else {
                                    "网络请求失败，请稍后重试"
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析错误响应失败", e)
                                "网络请求失败，请稍后重试"
                            }

                            Log.e(TAG, "AI使用量API请求失败: $errorMessage")
                            callback(false, false, errorMessage)
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "AI使用量API请求异常", result.throwable)
                            callback(false, false, result.throwable.message)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "检查AI使用量异常", e)
                    callback(false, false, e.message)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查AI使用量失败", e)
            callback(false, false, e.message)
        }
    }

    /**
     * 检查任务状态并继续处理
     *
     * @param context 上下文
     * @param noteId 笔记ID
     */
    private fun checkTaskAndProceed(context: Context, noteId: String) {
        Log.d(TAG, "开始检查任务状态")

        // 检查是否正在更新笔记
        if (NoteApi.isUpdating) {
            Log.d(TAG, "检测到正在更新笔记，忽略本次操作")
            CustomToastHelper.showToast(context, "正在更新笔记中，请稍候...")
            return
        }

        val taskApi = TaskApi.getInstance(context)
        taskApi.checkCreateNoteTask { success, hasTask, taskId, error ->
            // 在主线程处理结果
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                if (success) {
                    if (hasTask) {
                        Log.d(TAG, "检测到正在进行的笔记创建任务: $taskId")
                        CustomToastHelper.showToast(context, "请先完成当前笔记任务，无法同时进行多个笔记操作")
                        // 清空临时数据
                        clearTemporaryData()
                    } else {
                        Log.d(TAG, "没有正在进行的笔记任务，继续处理")
                        // 设置更新状态
                        NoteApi.isUpdating = true
                        // 继续执行内容提取逻辑
                        extractContentFromClipboard(context, noteId)
                    }
                } else {
                    Log.e(TAG, "检查笔记任务失败: $error")
                    // 检查失败，为了用户体验，仍然继续执行更新逻辑
                    NoteApi.isUpdating = true
                    extractContentFromClipboard(context, noteId)
                }
            }
        }
    }

    /**
     * 从剪贴板提取内容并处理
     *
     * @param context 上下文
     * @param noteId 笔记ID
     */
    private fun extractContentFromClipboard(context: Context, noteId: String) {
        Log.d(TAG, "开始从剪贴板提取内容")

        try {
            // 获取剪贴板内容
            val clipboardHelper = ClipboardHelper.getInstance(context)
            val clipText = clipboardHelper.getClipboardText()

            if (clipText.isNullOrEmpty()) {
                Log.w(TAG, "剪贴板内容为空")
                CustomToastHelper.showToast(context, "剪贴板内容为空")
                clearTemporaryData()
                return
            }

            // 提取链接
            val extractedLinks = ClipboardLinkExtractor.extractLinksFromText(clipText)
            if (extractedLinks.isEmpty()) {
                Log.w(TAG, "剪贴板中未找到有效链接")
                CustomToastHelper.showToast(context, "剪贴板中未找到有效链接")
                clearTemporaryData()
                return
            }

            Log.d(TAG, "提取到 ${extractedLinks.size} 个链接")

            // 使用第一个链接进行处理
            val firstUrl = extractedLinks.first()
            processUrlForUpdate(context, firstUrl, noteId)

        } catch (e: Exception) {
            Log.e(TAG, "从剪贴板提取内容失败", e)
            CustomToastHelper.showToast(context, "提取内容失败: ${e.message}")
            clearTemporaryData()
        }
    }

    /**
     * 处理URL进行更新
     *
     * @param context 上下文
     * @param url URL地址
     * @param noteId 笔记ID
     */
    private fun processUrlForUpdate(context: Context, url: String, noteId: String) {
        Log.d(TAG, "开始处理URL进行更新: $url")

        try {
            // 使用WebViewHtmlExtractor执行extractVideo.js脚本提取视频和封面信息
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context = context,
                url = url,
                businessName = "extractVideo"
            ) { result: String?, error: String? ->
                if (error != null) {
                    Log.e(TAG, "视频提取失败 - URL: $url, 错误: $error")
                    CustomToastHelper.showToast(context, "视频解析失败")
                    clearTemporaryData()
                } else if (result != null) {
                    Log.d(TAG, "视频提取成功，结果: $result")

                    try {
                        // 解析JSON结果
                        val jsonResult = org.json.JSONObject(result)
                        val success = jsonResult.optBoolean("success", false)

                        if (success) {
                            // 提取封面信息
                            val cover = jsonResult.optString("cover", "")
                            if (cover.isNotEmpty()) {
                                NoteApi.currentNoteCover = cover
                                Log.d(TAG, "已保存封面URL: $cover")
                            }

                            // 提取视频信息
                            val videosArray = jsonResult.optJSONArray("videos")
                            val videoUrls = mutableListOf<String>()

                            if (videosArray != null && videosArray.length() > 0) {
                                for (i in 0 until videosArray.length()) {
                                    val videoObj = videosArray.getJSONObject(i)
                                    val videoSrc = videoObj.optString("src", "")
                                    val videoCurrentSrc = videoObj.optString("currentSrc", "")

                                    // 优先使用currentSrc，如果没有则使用src
                                    val videoUrl = when {
                                        videoCurrentSrc.isNotEmpty() && videoCurrentSrc != "null" -> videoCurrentSrc
                                        videoSrc.isNotEmpty() && videoSrc != "null" -> videoSrc
                                        else -> ""
                                    }

                                    if (videoUrl.isNotEmpty()) {
                                        videoUrls.add(videoUrl)
                                    }
                                }
                            }

                            Log.d(TAG, "提取到 ${videoUrls.size} 个视频URL")

                            // 处理视频下载和上传
                            if (videoUrls.isNotEmpty()) {
                                processVideoDownloadAndUpload(context, videoUrls, noteId)
                            } else {
                                CustomToastHelper.showToast(context, "视频内容解析为空")
                            }
                        } else {
                            Log.w(TAG, "视频解析异常")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解析视频提取结果失败", e)
                        CustomToastHelper.showToast(context, "解析提取结果失败")
                        clearTemporaryData()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理URL失败", e)
            CustomToastHelper.showToast(context, "处理URL失败: ${e.message}")
            clearTemporaryData()
        }
    }

    /**
     * 处理视频下载和上传
     *
     * @param context 上下文
     * @param videoUrls 视频URL列表
     * @param noteId 笔记ID
     */
    private fun processVideoDownloadAndUpload(
        context: Context,
        videoUrls: List<String>,
        noteId: String
    ) {
        Log.d(TAG, "开始处理视频下载和上传")

        // 只处理第一个视频URL
        val videoUrl = videoUrls.first()
        Log.d(TAG, "开始下载视频: $videoUrl")

        val ffmpegHelper = FFmpegHelper.getInstance(context)
        val finalUserAgent = UserAgentGenerator.generateUserAgentForUrl(videoUrl)
        val finalReferer = UserAgentGenerator.getRefererForUrl(videoUrl)

        ffmpegHelper.downloadVideo(
            url = videoUrl,
            userAgent = finalUserAgent,
            referer = finalReferer,
            outputPath = null, // 使用默认路径
            saveToGallery = false, // 保存到相册
            callback = { success, localPath, error ->
                if (success && !localPath.isNullOrEmpty()) {
                    Log.d(TAG, "视频下载成功: $localPath")
                    // 上传到OSS
                    uploadVideoToOss(context, localPath, videoUrl, noteId)
                } else {
                    Log.e(TAG, "视频下载失败: $error")
                    CustomToastHelper.showToast(context, "视频下载失败")
                    clearTemporaryData()
                }
            }
        )
    }

    /**
     * 上传视频到OSS
     *
     * @param context 上下文
     * @param localPath 本地视频路径
     * @param originalUrl 原始视频URL
     * @param noteId 笔记ID
     */
    private fun uploadVideoToOss(
        context: Context,
        localPath: String,
        originalUrl: String,
        noteId: String
    ) {
        Log.d(TAG, "开始上传视频到OSS: $localPath")

        // 在后台线程执行OSS上传
        Thread {
            try {
                val ossManager = OssManager.getInstance(context)

                // 生成OSS文件名
                val fileName =
                    "videos/${System.currentTimeMillis()}_${java.util.UUID.randomUUID()}.mp4"

                // 使用同步方法上传文件
                val ossUrl = ossManager.uploadFileSync(localPath, fileName)

                // 在主线程中执行回调
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    Log.d(TAG, "视频上传OSS成功: $ossUrl")
                    // 继续创建更新任务
                    createUpdateTask(context, ossUrl, noteId)
                }
            } catch (e: Exception) {
                // 在主线程中执行回调
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    Log.e(TAG, "视频上传OSS失败: ${e.message}", e)
                    CustomToastHelper.showToast(context, "视频上传失败: ${e.message}")
                    clearTemporaryData()
                }
            }
        }.start()
    }

    /**
     * 创建更新任务
     *
     * @param context 上下文
     * @param url URL地址
     * @param noteId 笔记ID
     */
    private fun createUpdateTask(context: Context, url: String, noteId: String) {
        Log.d(TAG, "开始创建更新任务")

        val taskApi = TaskApi.getInstance(context)

        // 创建笔记更新任务（使用任务类型4=更新笔记，因为后端处理逻辑相同）
        taskApi.createTask(
            taskType = 4, // 更新笔记任务类型
            title = "更新笔记",
            platform = UrlPlatformExtractor.extractPlatformFromUrl(url),
            url = url,
            sourceType = 1, // 音视频内容
            noteId = noteId // 传递笔记ID
        ) { success, result, error ->
            if (success && result != null) {
                try {
                    val jsonResponse = JSONObject(result)
                    val taskId = jsonResponse.optString("id", "")

                    if (taskId.isNotEmpty()) {
                        Log.d(TAG, "更新任务创建成功，任务ID: $taskId")

                        // 开始轮询任务状态
                        startTaskPolling(context, taskId, url, noteId, 4)
                    } else {
                        Log.e(TAG, "任务ID为空")
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            CustomToastHelper.showToast(context, "任务ID为空")
                        }
                        clearTemporaryData()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析任务创建响应失败", e)
                    clearTemporaryData()
                }
            } else {
                clearTemporaryData()
            }
        }
    }

    /**
     * 开始任务状态轮询
     *
     * @param context 上下文
     * @param taskId 任务ID
     * @param url URL地址
     * @param noteId 笔记ID
     * @param taskType 任务类型
     */
    private fun startTaskPolling(
        context: Context,
        taskId: String,
        url: String,
        noteId: String,
        taskType: Int = 3
    ) {
        Log.d(TAG, "开始轮询任务状态: $taskId")

        TaskStatusPoller.getInstance().startPolling(
            context = context,
            taskId = taskId,
            taskType = taskType, // 创建笔记任务类型
            taskTitle = "更新笔记",
            platform = UrlPlatformExtractor.extractPlatformFromUrl(url),
            url = url,
            sourceType = 1
        )
    }

    /**
     * 清空临时数据
     */
    private fun clearTemporaryData() {
        Log.d(TAG, "清空临时数据")
        NoteApi.currentUpdateNoteId = ""
        NoteApi.currentUpdateNoteContent = ""
        NoteApi.currentNoteCover = ""
        NoteApi.isUpdating = false
    }

    /**
     * 检查是否正在更新笔记
     *
     * @return 是否正在更新
     */
    fun isUpdating(): Boolean {
        return NoteApi.isUpdating
    }

    /**
     * 重置更新状态
     * 用于异常情况下的状态重置
     */
    fun resetUpdatingState() {
        clearTemporaryData()
        Log.d(TAG, "重置更新状态")
    }
}
