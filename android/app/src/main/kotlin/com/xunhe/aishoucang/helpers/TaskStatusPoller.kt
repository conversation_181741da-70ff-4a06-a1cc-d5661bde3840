package com.xunhe.aishoucang.helpers

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.JavascriptInterface
import android.annotation.SuppressLint
import com.xunhe.aishoucang.api.TaskApi
import com.xunhe.aishoucang.api.NoteApi
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.CustomToastModalHelper
import org.json.JSONObject
import kotlinx.coroutines.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 任务状态轮询器
 * 用于轮询查询任务状态，并在状态变化时通知用户
 */
class TaskStatusPoller private constructor() {
    companion object {
        private const val TAG = "TaskStatusPoller"
        private const val POLL_INTERVAL = 2000L // 3秒轮询一次
        private const val MAX_POLL_COUNT = 100 // 最多轮询100次（5分钟）

        @Volatile
        private var INSTANCE: TaskStatusPoller? = null

        fun getInstance(): TaskStatusPoller {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TaskStatusPoller().also { INSTANCE = it }
            }
        }
    }

    private val handler = Handler(Looper.getMainLooper())
    private val pollingTasks = mutableMapOf<String, PollingTask>()

    /**
     * 轮询任务数据类
     */
    private data class PollingTask(
        val taskId: String,
        val context: Context,
        val taskApi: TaskApi,
        var pollCount: Int = 0,
        val runnable: Runnable,
        val taskType: Int = 0,
        val taskTitle: String = "",
        val platform: String = "",
        val url: String = "",
        val sourceType: Int = 0
    )

    /**
     * 开始轮询任务状态
     *
     * @param context 上下文
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param taskTitle 任务标题
     * @param platform 平台
     * @param url URL
     * @param sourceType 内容源类型
     */
    fun startPolling(
        context: Context,
        taskId: String,
        taskType: Int = 0,
        taskTitle: String = "",
        platform: String = "",
        url: String = "",
        sourceType: Int = 0
    ) {
        Log.d(TAG, "开始轮询任务状态: $taskId")

        // 如果已经在轮询这个任务，先停止
        stopPolling(taskId)

        val taskApi = TaskApi.getInstance(context)

        val pollingRunnable = object : Runnable {
            override fun run() {
                val pollingTask = pollingTasks[taskId]
                if (pollingTask == null) {
                    Log.d(TAG, "轮询任务已被移除: $taskId")
                    return
                }

                pollingTask.pollCount++
                Log.d(TAG, "轮询任务状态 - 任务ID: $taskId, 第${pollingTask.pollCount}次")

                // 检查是否超过最大轮询次数
                if (pollingTask.pollCount > MAX_POLL_COUNT) {
                    Log.w(TAG, "任务轮询超时: $taskId")
                    stopPolling(taskId)
                    showTimeoutModal(context)
                    return
                }

                // 查询任务状态
                taskApi.getTaskStatus(taskId) { success, result, error ->
                    handler.post {
                        if (success && result != null) {
                            try {
                                val taskData = JSONObject(result)
                                val status = taskData.optInt("status", 1)
                                val result = taskData.optString("result", "")

                                Log.d(TAG, "任务状态查询结果 - 任务ID: $taskId, 状态: $status")

                                when (status) {
                                    1 -> {
                                        // 状态为1（进行中），继续轮询
                                        Log.d(TAG, "任务进行中，继续轮询: $taskId")
                                        handler.postDelayed(this, POLL_INTERVAL)
                                    }
                                    2 -> {
                                        // 状态为2（成功），停止轮询并提示用户
                                        Log.d(TAG, "任务完成成功: $taskId")
                                        val currentPollingTask = pollingTasks[taskId]
                                        stopPolling(taskId)

                                        if (currentPollingTask != null) {
                                            // 根据任务类型分发处理
                                            handleTaskCompletion(
                                                context,
                                                currentPollingTask,
                                                taskData,
                                                result
                                            )
                                        } else {
                                            // 兜底处理
                                            showSuccessModal(context, taskData)
                                        }
                                    }
                                    3 -> {
                                        // 状态为3（失败），停止轮询并提示用户
                                        Log.d(TAG, "任务执行失败: $taskId")
                                        stopPolling(taskId)
                                        showFailureModal(context, taskData)
                                    }
                                    else -> {
                                        // 未知状态，立即停止轮询
                                        Log.w(TAG, "未知任务状态: $status, 停止轮询: $taskId")
                                        stopPolling(taskId)
                                        showErrorModal(context, "任务状态异常，请稍后重试")
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析任务状态数据失败: $taskId", e)
                                // 解析失败，立即停止轮询
                                stopPolling(taskId)
                                showErrorModal(context, "解析任务状态失败，请稍后重试")
                            }
                        } else {
                            Log.e(TAG, "查询任务状态失败: $taskId, 错误: $error")
                            // 查询失败，立即停止轮询
                            stopPolling(taskId)
                            showErrorModal(context, "查询任务状态失败，请稍后重试")
                        }
                    }
                }
            }
        }

        // 创建轮询任务
        val pollingTask = PollingTask(
            taskId = taskId,
            context = context,
            taskApi = taskApi,
            runnable = pollingRunnable,
            taskType = taskType,
            taskTitle = taskTitle,
            platform = platform,
            url = url,
            sourceType = sourceType
        )

        pollingTasks[taskId] = pollingTask

        // 开始第一次轮询
        handler.post(pollingRunnable)
    }

    /**
     * 停止轮询任务状态
     *
     * @param taskId 任务ID
     */
    fun stopPolling(taskId: String) {
        val pollingTask = pollingTasks.remove(taskId)
        if (pollingTask != null) {
            handler.removeCallbacks(pollingTask.runnable)
            Log.d(TAG, "停止轮询任务状态: $taskId")
        }
    }

    /**
     * 停止所有轮询任务
     */
    fun stopAllPolling() {
        Log.d(TAG, "停止所有轮询任务")
        pollingTasks.values.forEach { pollingTask ->
            handler.removeCallbacks(pollingTask.runnable)
        }
        pollingTasks.clear()
    }

    /**
     * 处理任务完成
     * 根据任务类型分发到不同的处理函数
     */
    private fun handleTaskCompletion(
        context: Context,
        pollingTask: PollingTask,
        taskData: JSONObject,
        result: String
    ) {
        Log.d(TAG, "处理任务完成 - 任务类型: ${pollingTask.taskType}, 标题: ${pollingTask.taskTitle}")

        when (pollingTask.taskType) {
            2 -> {
                // 任务类型2：图片处理任务
                handleImageProcessingTaskCompletion(context, pollingTask, taskData, result)
            }
            3 -> {
                // 任务类型3：笔记创建任务
                handleNoteCreationTaskCompletion(context, pollingTask, taskData, result)
            }
            4 -> {
                // 任务类型4：笔记更新任务
                handleNoteUpdateTaskCompletion(context, pollingTask, taskData, result)
            }
            else -> {
                // 未知任务类型，使用默认处理
                Log.w(TAG, "未知任务类型: ${pollingTask.taskType}, 使用默认处理")
                showSuccessModal(context, taskData)
            }
        }
    }



    /**
     * 处理图片处理任务完成
     */
    private fun handleImageProcessingTaskCompletion(
        context: Context,
        pollingTask: PollingTask,
        taskData: JSONObject,
        result: String
    ) {
        Log.d(TAG, "处理图片处理任务完成: ${pollingTask.taskTitle}")

        // 显示图片处理完成的通知
        showTaskCompletionNotification(context, "图片处理已完成")

        // 可以在这里添加图片处理特有的处理逻辑
    }

    /**
     * 处理笔记创建任务完成
     */
    private fun handleNoteCreationTaskCompletion(
        context: Context,
        pollingTask: PollingTask,
        taskData: JSONObject,
        result: String
    ) {
        Log.d(TAG, "处理笔记创建任务完成: ${pollingTask.taskTitle}")

        // 处理创建笔记任务完成
        if (result.isNotEmpty()) {
            callOpenAiWithResult(context, result, pollingTask.taskTitle)
        } else {
            Log.w(TAG, "笔记创建任务结果为空，跳过OpenAI调用")
        }
    }

    /**
     * 处理笔记更新任务完成
     */
    private fun handleNoteUpdateTaskCompletion(
        context: Context,
        pollingTask: PollingTask,
        taskData: JSONObject,
        result: String
    ) {
        Log.d(TAG, "处理笔记更新任务完成: ${pollingTask.taskTitle}")
        Log.d(TAG, "更新任务详情: $taskData")

        // 用result调用OpenAI生成H5页面，然后更新笔记
        if (result.isNotEmpty()) {
            callOpenAiForNoteUpdate(context, result, pollingTask.taskTitle, taskData)
        } else {
            Log.w(TAG, "笔记更新任务结果为空，跳过OpenAI调用")
            // 清空更新状态
            com.xunhe.aishoucang.api.NoteApi.isUpdating = false
            com.xunhe.aishoucang.api.NoteApi.currentUpdateNoteId = ""
            com.xunhe.aishoucang.api.NoteApi.currentUpdateNoteContent = ""
            com.xunhe.aishoucang.api.NoteApi.currentNoteCover = ""
        }
    }

    /**
     * 显示任务完成通知
     */
    private fun showTaskCompletionNotification(context: Context, message: String) {
        CustomToastHelper.showToast(context, message)
    }

    /**
     * 显示任务成功完成的Modal
     */
    private fun showSuccessModal(context: Context, taskData: JSONObject) {
        val title = taskData.optString("title", "任务")

        // 直接使用任务标题加"已完成"
        val content = "${title}已完成"

        CustomToastModalHelper.showModal(
            context = context,
            title = "任务完成",
            content = content,
            cancelText = "知道了",
            confirmText = "查看",
            callback = object : CustomToastModalHelper.ModalCallback {
                override fun onConfirm() {
                    // TODO: 跳转到任务详情或结果页面
                    Log.d(TAG, "用户选择查看任务结果")
                }

                override fun onCancel() {
                    Log.d(TAG, "用户确认任务完成")
                }
            }
        )
    }

    /**
     * 显示任务失败的Modal
     */
    private fun showFailureModal(context: Context, taskData: JSONObject) {
        val title = taskData.optString("title", "任务")

        // 直接使用任务标题加"执行失败"
        val content = "${title}执行失败，请稍后重试"

        CustomToastModalHelper.showModal(
            context = context,
            title = "任务失败",
            content = content,
            cancelText = "知道了",
            confirmText = "重试",
            callback = object : CustomToastModalHelper.ModalCallback {
                override fun onConfirm() {
                    // TODO: 重新创建任务
                    Log.d(TAG, "用户选择重试任务")
                }

                override fun onCancel() {
                    Log.d(TAG, "用户确认任务失败")
                }
            }
        )
    }

    /**
     * 显示轮询超时的Modal
     */
    private fun showTimeoutModal(context: Context) {
        CustomToastModalHelper.showModal(
            context = context,
            title = "任务超时",
            content = "任务执行时间过长，请稍后手动查看任务状态",
            cancelText = "知道了",
            confirmText = "查看任务",
            callback = object : CustomToastModalHelper.ModalCallback {
                override fun onConfirm() {
                    // TODO: 跳转到任务列表页面
                    Log.d(TAG, "用户选择查看任务列表")
                }

                override fun onCancel() {
                    Log.d(TAG, "用户确认任务超时")
                }
            }
        )
    }

    /**
     * 获取当前轮询任务数量
     */
    fun getPollingTaskCount(): Int {
        return pollingTasks.size
    }

    /**
     * 检查是否正在轮询指定任务
     */
    fun isPolling(taskId: String): Boolean {
        return pollingTasks.containsKey(taskId)
    }

    /**
     * 用任务结果调用OpenAI并打印返回结果
     *
     * @param context 上下文
     * @param result 任务执行结果
     * @param noteTitle 笔记标题
     */
    private fun callOpenAiWithResult(context: Context, result: String, noteTitle: String = "") {
        Log.d(TAG, "开始用任务结果调用OpenAI...")
        Log.d(TAG, "任务结果内容: ${result.take(200)}${if (result.length > 200) "..." else ""}")

        val noteApi = NoteApi()

        // 调用AI生成H5页面
        noteApi.generateNoteByAi(context, result, noteTitle) { success, aiContent, error ->
            if (success && aiContent != null) {
                Log.d(TAG, "=== OpenAI调用成功 ===")
                Log.d(TAG, "原始任务结果: $result")
                Log.d(TAG, "AI生成的H5页面内容:")
                Log.d(TAG, aiContent)
                Log.d(TAG, "========================")

                // 可以在这里添加其他处理逻辑，比如保存生成的H5页面
                // handleAiGeneratedContent(aiContent) // 已移除，功能已整合到其他方法中
            } else {
                Log.e(TAG, "=== OpenAI调用失败 ===")
                Log.e(TAG, "任务结果: $result")
                Log.e(TAG, "错误信息: $error")
                Log.e(TAG, "========================")
            }
        }
    }

    /**
     * 调用OpenAI进行笔记更新
     *
     * @param context 上下文
     * @param result 任务结果
     * @param noteTitle 笔记标题
     */
    private fun callOpenAiForNoteUpdate(context: Context, result: String, noteTitle: String = "",taskData: JSONObject,) {
        Log.d(TAG, "开始用任务结果调用OpenAI进行笔记更新...")
        Log.d(TAG, "任务结果内容: ${result.take(200)}${if (result.length > 200) "..." else ""}")

        val noteApi = NoteApi()

        // 调用AI生成H5页面
        noteApi.generateNoteDraftByAi(context, result, noteTitle, taskData) { success, aiContent, error ->
            if (success && aiContent != null) {
                Log.d(TAG, "=== OpenAI调用成功，开始更新笔记 ===")
                Log.d(TAG, "原始任务结果: $result")
                Log.d(TAG, "AI生成的H5页面内容:")
                Log.d(TAG, aiContent)
                Log.d(TAG, "========================")

                // 更新笔记
                updateNoteWithAiContent(context, taskData, aiContent)
            } else {
                // 清空更新状态
                com.xunhe.aishoucang.api.NoteApi.isUpdating = false
                com.xunhe.aishoucang.api.NoteApi.currentUpdateNoteId = ""
                com.xunhe.aishoucang.api.NoteApi.currentUpdateNoteContent = ""
                com.xunhe.aishoucang.api.NoteApi.currentNoteCover = ""

                // 显示错误提示
                CustomToastHelper.showToast(context, "更新笔记失败")
            }
        }
    }

    /**
     * 使用AI生成的内容更新笔记
     *
     * @param context 上下文
     * @param taskData 任务数据
     * @param aiContent AI生成的内容
     */
    private fun updateNoteWithAiContent(context: Context, taskData: JSONObject, aiContent: String) {
        Log.d(TAG, "开始使用AI生成的内容更新笔记")

        // 使用协程执行异步操作
        CoroutineScope(Dispatchers.Main).launch {
            try {
                // 执行WebView加载和JS执行
                val result = executeWebViewWithJavaScript(context, NoteApi.currentUpdateNoteContent, aiContent)
                Log.d(TAG, "WebView执行完成，结果: $result")

                // 执行实际的笔记更新
                performNoteUpdate(context, taskData, result)

            } catch (e: Exception) {
                Log.e(TAG, "更新笔记过程中出错: ${e.message}", e)
                handleUpdateError(context, e.message ?: "未知错误")
            }
        }
    }

    /**
     * 使用WebView加载HTML内容并执行JavaScript (async/await格式)
     *
     * @param context 上下文
     * @param htmlContent 要加载的HTML内容
     * @param jsContent 要执行的JavaScript内容
     * @return 执行结果
     */
    @SuppressLint("SetJavaScriptEnabled")
    private suspend fun executeWebViewWithJavaScript(
        context: Context,
        htmlContent: String,
        jsContent: String
    ): String = suspendCoroutine { continuation ->

        Log.d(TAG, "开始创建WebView并加载HTML内容")
        Log.d(TAG, "HTML内容长度: ${htmlContent.length}")
        Log.d(TAG, "JS内容长度: ${jsContent.length}")

        val mainHandler = Handler(Looper.getMainLooper())
        var webView: WebView? = null
        var isCompleted = false

        mainHandler.post {
            try {
                // 创建WebView
                webView = WebView(context)

                // 配置WebView
                webView?.settings?.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    loadsImagesAutomatically = true
                }

                // 添加JavaScript接口用于接收执行结果
                webView?.addJavascriptInterface(object {
                    @JavascriptInterface
                    fun onResult(result: String) {
                        Log.d(TAG, "JavaScript执行完成")
                        Log.d(TAG, "=== 执行后的HTML内容 ===")
                        Log.d(TAG, result)
                        Log.d(TAG, "=== HTML内容结束 ===")
                        Log.d(TAG, "HTML内容长度: ${result.length} 字符")

                        if (!isCompleted) {
                            isCompleted = true
                            // 销毁WebView
                            mainHandler.post {
                                try {
                                    webView?.destroy()
                                } catch (e: Exception) {
                                    Log.e(TAG, "销毁WebView时出错: ${e.message}")
                                }
                            }
                            continuation.resume(result)
                        }
                    }

                    @JavascriptInterface
                    fun onError(error: String) {
                        Log.e(TAG, "JavaScript执行出错: $error")
                        if (!isCompleted) {
                            isCompleted = true
                            // 销毁WebView
                            mainHandler.post {
                                try {
                                    webView?.destroy()
                                } catch (e: Exception) {
                                    Log.e(TAG, "销毁WebView时出错: ${e.message}")
                                }
                            }
                            continuation.resume("ERROR: $error")
                        }
                    }
                }, "AndroidInterface")

                // 设置WebViewClient
                webView?.webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        Log.d(TAG, "WebView页面加载完成，开始执行JavaScript")

                        // 页面加载完成后执行JavaScript
                        val wrappedJs = """
                            try {
                                $jsContent
                                // 执行完成后获取整个HTML内容并返回
                                var htmlContent = document.documentElement.outerHTML;
                                AndroidInterface.onResult(htmlContent);
                            } catch (error) {
                                AndroidInterface.onError(error.toString());
                            }
                        """.trimIndent()

                        view?.evaluateJavascript(wrappedJs) { result ->
                            Log.d(TAG, "JavaScript评估结果: $result")
                        }
                    }

                    override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                        super.onReceivedError(view, errorCode, description, failingUrl)
                        Log.e(TAG, "WebView加载出错: $description")
                        if (!isCompleted) {
                            isCompleted = true
                            continuation.resume("ERROR: WebView加载失败 - $description")
                        }
                    }
                }

                // 加载HTML内容
                webView?.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)

                // 设置超时处理
                mainHandler.postDelayed({
                    if (!isCompleted) {
                        isCompleted = true
                        Log.w(TAG, "WebView执行超时")
                        try {
                            webView?.destroy()
                        } catch (e: Exception) {
                            Log.e(TAG, "销毁WebView时出错: ${e.message}")
                        }
                        continuation.resume("ERROR: 执行超时")
                    }
                }, 30000) // 30秒超时

            } catch (e: Exception) {
                Log.e(TAG, "创建WebView时出错: ${e.message}", e)
                if (!isCompleted) {
                    isCompleted = true
                    continuation.resume("ERROR: ${e.message}")
                }
            }
        }
    }

    /**
     * 执行实际的笔记更新操作
     *
     * @param context 上下文
     * @param taskData 任务数据
     * @param webViewResult WebView执行结果
     */
    private fun performNoteUpdate(context: Context, taskData: JSONObject, webViewResult: String) {
        Log.d(TAG, "开始执行笔记更新操作")
        Log.d(TAG, "WebView结果长度: ${webViewResult.length}")

        // 获取笔记ID
        val noteId = NoteApi.currentUpdateNoteId
        if (noteId.isEmpty()) {
            Log.e(TAG, "笔记ID为空，无法创建草稿")
            handleUpdateError(context, "笔记ID为空")
            return
        }

        // 检查WebView结果是否有效
        if (webViewResult.isEmpty() || webViewResult.startsWith("ERROR:")) {
            Log.e(TAG, "WebView执行结果无效: $webViewResult")
            handleUpdateError(context, "页面处理失败")
            return
        }

        // 创建NoteApi实例并调用创建草稿方法
        val noteApi = NoteApi()
        noteApi.createNoteDraft(context, noteId, webViewResult) { success, draftId, error ->
            if (success && draftId != null) {
                Log.d(TAG, "笔记草稿创建成功: draftId=$draftId")

                // 显示成功提示模态框
                CustomToastModalHelper.showModal(
                    context = context,
                    title = "笔记更新完成",
                    content = "笔记更新已完成，是否要查看草稿？",
                    cancelText = "稍后查看",
                    confirmText = "立即查看",
                    callback = object : CustomToastModalHelper.ModalCallback {
                        override fun onConfirm() {
                            Log.d(TAG, "用户选择立即查看草稿")
                        }

                        override fun onCancel() {
                            Log.d(TAG, "用户选择稍后查看草稿")
                        }
                    }
                )

                // 清空临时数据
                clearUpdateTemporaryData()
            } else {
                Log.e(TAG, "笔记草稿创建失败: $error")
                handleUpdateError(context, error ?: "创建草稿失败")
            }
        }
    }

    /**
     * 处理更新错误
     *
     * @param context 上下文
     * @param errorMessage 错误信息
     */
    private fun handleUpdateError(context: Context, errorMessage: String) {
        Log.e(TAG, "处理更新错误: $errorMessage")

        // 显示错误提示
        CustomToastHelper.showToast(context, "更新笔记失败: $errorMessage")

        // 清空临时数据
        clearUpdateTemporaryData()
    }

    /**
     * 清空更新相关的临时数据
     */
    private fun clearUpdateTemporaryData() {
        Log.d(TAG, "清空更新临时数据")
        NoteApi.isUpdating = false
        NoteApi.currentUpdateNoteId = ""
        NoteApi.currentUpdateNoteContent = ""
        NoteApi.currentNoteCover = ""
    }

    /**
     * 显示轮询错误的Modal
     */
    private fun showErrorModal(context: Context, message: String) {
        CustomToastModalHelper.showModal(
            context = context,
            title = "任务轮询错误",
            content = message,
            cancelText = "知道了",
            confirmText = "重试",
            callback = object : CustomToastModalHelper.ModalCallback {
                override fun onConfirm() {
                    // TODO: 重新开始轮询
                    Log.d(TAG, "用户选择重试轮询")
                }

                override fun onCancel() {
                    Log.d(TAG, "用户确认轮询错误")
                }
            }
        )
    }
}
