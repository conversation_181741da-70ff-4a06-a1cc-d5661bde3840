package com.xunhe.aishoucang.helpers

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.*
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.FavoriteItemHandler
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 豆瓣应用收藏项处理器
 */
object DoubanAppItemHandler {
    private const val TAG = "DoubanAppItemHandler"

    // 时间格式化工具
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    /**
     * 用于保存提取的豆瓣数据
     */
    private class ExtractedData {
        var avatarUrl: String? = null
        var title: String? = null
        var description: String? = null
        var imageUrl: String? = null
        var authorName: String? = null
        var schemeURL: String? = null

        // 检查是否所有数据都已准备就绪
        fun isComplete(): Boolean {
            return title != null
        }

        // 处理完成后的操作
        fun processResult(context: Context, favoriteItem: SharePanelItem?, startTime: Long) {
            Log.d(TAG, "所有数据已准备就绪，开始处理结果")
            Log.i(TAG, "【时间日志】开始处理结果: ${getElapsedTimeLog(startTime)}")

            // 使用协程在后台线程处理
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                Log.d(TAG, "提取结果汇总:")
                Log.d(TAG, "- 用户名: ${authorName ?: "未知"}")
                Log.d(TAG, "- 标题: ${title ?: "未知"}")
                Log.d(TAG, "- 描述: ${description ?: "未知"}")
                Log.d(TAG, "- 图片URL: ${imageUrl ?: "未知"}")
                Log.d(TAG, "- 头像URL: ${avatarUrl ?: "未找到"}")
                Log.d(TAG, "- 自定义URL: ${schemeURL ?: "未获取"}")

                // 获取平台类型
                val platformType = SharePanelHelper.getCurrentPlatformType(schemeURL)

                // 在主线程中调用BookMark.addBookMark
                Log.i(TAG, "【时间日志】准备调用BookMark.addBookMark: ${getElapsedTimeLog(startTime)}")
                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                    BookMark.addBookMark(
                        context,
                        influencer_name = authorName,
                        influencer_avatar = avatarUrl,
                        cover = imageUrl,
                        title = title,
                        desc = description,
                        parent_id = favoriteItem?.id ?: "",
                        scheme_url = schemeURL ?: "",
                        platform_type = platformType,
                        callback = { success, errorMessage ->
                            Handler(Looper.getMainLooper()).post {
                                Log.i(TAG, "【时间日志】收藏完成: ${getElapsedTimeLog(startTime)}")

                                if (success) {
                                    CustomToastHelper.showShortToast(context, "收藏成功")
                                    Log.i(TAG, "【时间日志】收藏成功: ${getElapsedTimeLog(startTime)}")
                                } else {
                                    CustomToastHelper.showShortToast(context, "保存失败: $errorMessage")
                                    Log.i(TAG, "【时间日志】收藏失败: ${getElapsedTimeLog(startTime)}, 错误: $errorMessage")
                                }
                            }
                        }
                    )
                }
            }
        }
    }

    /**
     * 处理豆瓣应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     * @param startTime 收藏开始时间（毫秒）
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?,
        startTime: Long = System.currentTimeMillis()
    ) {
        Log.i(TAG, "【时间日志】豆瓣处理器开始: ${getElapsedTimeLog(startTime)}")
        val contentType = SharePanelHelper.getCurrentContentType()

        when (contentType) {
            ContentTypeConstants.DOUBAN_TYPE_NOTE -> {
                SaveNote(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.DOUBAN_TYPE_MOVIE -> {
                SaveMovie(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.DOUBAN_TYPE_BOKE -> {
                SaveMovie(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.DOUBAN_TYPE_MUSIC -> {
                SaveMusic(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.DOUBAN_TYPE_GROUP -> {
                SaveGroup(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            else -> {
                CustomToastHelper.showToast(context, "已保存豆瓣内容到「${favoriteItem?.name}」")
            }
        }
    }

    /**
     * 豆瓣动态的收藏
     */
    fun SaveNote(context: Context, appPackage: String, clipboardContent: String?, favoriteItem: SharePanelItem?, startTime: Long) {
        Log.i(TAG, "【时间日志】开始处理豆瓣动态: ${getElapsedTimeLog(startTime)}")
        Log.i(TAG, "豆瓣剪切板内容: $clipboardContent")

        val extractedLink = extractDoubanLink(clipboardContent)

        if (extractedLink != null) {
            Log.d(TAG, "开始处理豆瓣链接: $extractedLink")

            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            // 创建数据实例
            val extractedData = ExtractedData()
            extractedData.schemeURL = extractedLink

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context = context,
                url = extractedLink,
                businessName = "DoubanStatus", // 对应assets/js/DoubanStatus.js文件
                replacements = emptyMap(), // 不需要替换参数
                callback = { result, error ->
                    if (error != null) {
                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                        Log.e(TAG, "提取豆瓣动态信息失败: $error")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                        return@executeBusinessJavaScript
                    }

                    if (result != null) {
                        try {
                            Log.i(TAG, "【时间日志】豆瓣动态数据提取成功: ${getElapsedTimeLog(startTime)}")

                            // 解析JSON结果
                            val jsonObject = org.json.JSONObject(result)

                            // 检查是否有错误
                            if (jsonObject.has("error")) {
                                val errorMessage = jsonObject.getString("error")
                                Log.e(TAG, "提取豆瓣动态数据时JavaScript报错: $errorMessage")

                                // 显示错误消息
                                CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                                Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                                return@executeBusinessJavaScript
                            }

                            // 提取数据
                            val title = if (jsonObject.has("title")) jsonObject.getString("title") else null
                            val description = if (jsonObject.has("description")) jsonObject.getString("description") else null
                            val imageUrl = if (jsonObject.has("imageUrl")) jsonObject.getString("imageUrl") else null
                            val authorName = if (jsonObject.has("authorName")) jsonObject.getString("authorName") else null
                            val avatarUrl = if (jsonObject.has("avatarUrl")) jsonObject.getString("avatarUrl") else null

                            // 检查是否有拦截到的schemeURL
                            val interceptedSchemeUrl = if (result.contains("InterceptedSchemeUrl:")) {
                                val schemePattern = """InterceptedSchemeUrl: (.+)""".toRegex()
                                val schemeMatch = schemePattern.find(result)
                                schemeMatch?.groupValues?.getOrNull(1)
                            } else {
                                null
                            }

                            Log.d(TAG, "提取结果汇总:")
                            Log.d(TAG, "- 标题: ${title ?: "未知"}")
                            Log.d(TAG, "- 描述: ${description ?: "未知"}")
                            Log.d(TAG, "- 图片URL: ${imageUrl ?: "未知"}")
                            Log.d(TAG, "- 作者名称: ${authorName ?: "未知"}")
                            Log.d(TAG, "- 作者头像: ${avatarUrl ?: "未知"}")
                            Log.d(TAG, "- 拦截的SchemeURL: ${interceptedSchemeUrl ?: "未获取"}")

                            // 更新提取的数据
                            extractedData.title = title ?: "豆瓣动态"
                            extractedData.description = description ?: clipboardContent
                            extractedData.imageUrl = imageUrl
                            extractedData.authorName = authorName
                            extractedData.avatarUrl = avatarUrl

                            // 如果有拦截到的schemeURL，优先使用它
                            if (!interceptedSchemeUrl.isNullOrEmpty()) {
                                extractedData.schemeURL = interceptedSchemeUrl
                            }

                            // 处理结果
                            extractedData.processResult(context, favoriteItem, startTime)
                        } catch (e: Exception) {
                            Log.e(TAG, "解析豆瓣动态数据失败: ${e.message}", e)

                            // 显示错误消息
                            CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                            Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                        }
                    } else {
                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                        Log.e(TAG, "提取豆瓣动态信息失败: 结果为空")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                    }
                }
            )
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到豆瓣链接")
            CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 豆瓣电影的收藏
     */
    fun SaveMovie(context: Context, appPackage: String, clipboardContent: String?, favoriteItem: SharePanelItem?, startTime: Long) {
        Log.i(TAG, "【时间日志】开始处理豆瓣电影: ${getElapsedTimeLog(startTime)}")
        Log.i(TAG, "豆瓣剪切板内容: $clipboardContent")

        // 提取豆瓣链接
        val extractedLink = extractDoubanLink(clipboardContent)

        if (extractedLink != null) {
            Log.d(TAG, "开始处理豆瓣电影链接: $extractedLink")

            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            // 创建数据实例
            val extractedData = ExtractedData()
            extractedData.schemeURL = extractedLink

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context = context,
                url = extractedLink,
                businessName = "DoubanMovie", // 对应assets/js/DoubanMovie.js文件
                replacements = emptyMap(), // 不需要替换参数
                callback = { result, error ->
                    if (error != null) {
                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "提取豆瓣电影信息失败: ${error}")
                        Log.e(TAG, "提取豆瓣电影信息失败: $error")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                        return@executeBusinessJavaScript
                    }

                    if (result != null) {
                        try {
                            Log.i(TAG, "【时间日志】豆瓣电影数据提取成功: ${getElapsedTimeLog(startTime)}")

                            // 解析JSON结果
                            val jsonObject = org.json.JSONObject(result)

                            // 检查是否有错误
                            if (jsonObject.has("error")) {
                                val errorMessage = jsonObject.getString("error")
                                Log.e(TAG, "提取豆瓣电影数据时JavaScript报错: $errorMessage")

                                // 显示错误消息
                                CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                                Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                                return@executeBusinessJavaScript
                            }

                            // 提取数据
                            val title = if (jsonObject.has("title")) jsonObject.getString("title") else null
                            val description = if (jsonObject.has("description")) jsonObject.getString("description") else null
                            val coverUrl = if (jsonObject.has("coverUrl")) jsonObject.getString("coverUrl") else null
                            val authorName = if (jsonObject.has("authorName")) jsonObject.getString("authorName") else null
                            val avatarUrl = if (jsonObject.has("avatarUrl")) jsonObject.getString("avatarUrl") else null

                            // 检查是否有拦截到的schemeURL
                            val interceptedSchemeUrl = if (result.contains("InterceptedSchemeUrl:")) {
                                val schemePattern = """InterceptedSchemeUrl: (.+)""".toRegex()
                                val schemeMatch = schemePattern.find(result)
                                schemeMatch?.groupValues?.getOrNull(1)
                            } else {
                                null
                            }

                            Log.d(TAG, "提取结果汇总:")
                            Log.d(TAG, "- 标题: ${title ?: "未知"}")
                            Log.d(TAG, "- 描述: ${description ?: "未知"}")
                            Log.d(TAG, "- 封面URL: ${coverUrl ?: "未知"}")
                            Log.d(TAG, "- 导演名称: ${authorName ?: "未知"}")
                            Log.d(TAG, "- 头像: ${avatarUrl ?: "未知"}")
                            Log.d(TAG, "- 拦截的SchemeURL: ${interceptedSchemeUrl ?: "未获取"}")

                            // 更新提取的数据
                            extractedData.title = title ?: "豆瓣电影"
                            extractedData.description = description ?: clipboardContent
                            extractedData.imageUrl = coverUrl
                            extractedData.authorName = authorName
                            extractedData.avatarUrl = avatarUrl

                            // 如果有拦截到的schemeURL，优先使用它
                            if (!interceptedSchemeUrl.isNullOrEmpty()) {
                                extractedData.schemeURL = interceptedSchemeUrl
                            }

                            // 处理结果
                            extractedData.processResult(context, favoriteItem, startTime)
                        } catch (e: Exception) {
                            Log.e(TAG, "解析豆瓣电影数据失败: ${e.message}", e)

                            // 显示错误消息
                            CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                            Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                        }
                    } else {
                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                        Log.e(TAG, "提取豆瓣电影信息失败: 结果为空")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                    }
                }
            )
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到豆瓣链接")
            CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 豆瓣播客的收藏
     */
    fun SaveBoKe(context: Context, appPackage: String, clipboardContent: String?, favoriteItem: SharePanelItem?, startTime: Long) {
        Log.i(TAG, "【时间日志】开始处理豆瓣播客: ${getElapsedTimeLog(startTime)}")
        Log.i(TAG, "豆瓣剪切板内容: $clipboardContent")

        // 提取豆瓣链接
        val extractedLink = extractDoubanLink(clipboardContent)

        if (extractedLink != null) {
            Log.d(TAG, "开始处理豆瓣播客链接: $extractedLink")

            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            // 创建数据实例
            val extractedData = ExtractedData()
            extractedData.schemeURL = extractedLink

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context = context,
                url = extractedLink,
                businessName = "DoubanBoke", // 对应assets/js/DoubanBoke.js文件
                replacements = emptyMap(), // 不需要替换参数
                callback = { result, error ->
                    if (error != null) {
                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "提取豆瓣播客信息失败: ${error}")
                        Log.e(TAG, "提取豆瓣播客信息失败: $error")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                        return@executeBusinessJavaScript
                    }

                    if (result != null) {
                        try {
                            Log.i(TAG, "【时间日志】豆瓣播客数据提取成功: ${getElapsedTimeLog(startTime)}")

                            // 解析JSON结果
                            val jsonObject = org.json.JSONObject(result)

                            // 检查是否有错误
                            if (jsonObject.has("error")) {
                                val errorMessage = jsonObject.getString("error")
                                Log.e(TAG, "提取豆瓣播客数据时JavaScript报错: $errorMessage")

                                // 显示错误消息
                                CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                                Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                                return@executeBusinessJavaScript
                            }

                            // 提取数据
                            val title = if (jsonObject.has("title")) jsonObject.getString("title") else null
                            val description = if (jsonObject.has("description")) jsonObject.getString("description") else null
                            val imageUrl = if (jsonObject.has("imageUrl")) jsonObject.getString("imageUrl") else null
                            val authorName = if (jsonObject.has("authorName")) jsonObject.getString("authorName") else null
                            val avatarUrl = if (jsonObject.has("avatarUrl")) jsonObject.getString("avatarUrl") else null

                            // 检查是否有拦截到的schemeURL
                            val interceptedSchemeUrl = if (result.contains("InterceptedSchemeUrl:")) {
                                val schemePattern = """InterceptedSchemeUrl: (.+)""".toRegex()
                                val schemeMatch = schemePattern.find(result)
                                schemeMatch?.groupValues?.getOrNull(1)
                            } else {
                                null
                            }

                            Log.d(TAG, "提取结果汇总:")
                            Log.d(TAG, "- 标题: ${title ?: "未知"}")
                            Log.d(TAG, "- 描述: ${description ?: "未知"}")
                            Log.d(TAG, "- 图片URL: ${imageUrl ?: "未知"}")
                            Log.d(TAG, "- 主播名称: ${authorName ?: "未知"}")
                            Log.d(TAG, "- 头像: ${avatarUrl ?: "未知"}")
                            Log.d(TAG, "- 拦截的SchemeURL: ${interceptedSchemeUrl ?: "未获取"}")

                            // 更新提取的数据
                            extractedData.title = title ?: "豆瓣播客"
                            extractedData.description = description ?: clipboardContent
                            extractedData.imageUrl = imageUrl
                            extractedData.authorName = authorName
                            extractedData.avatarUrl = avatarUrl

                            // 如果有拦截到的schemeURL，优先使用它
                            if (!interceptedSchemeUrl.isNullOrEmpty()) {
                                extractedData.schemeURL = interceptedSchemeUrl
                            }

                            // 处理结果
                            extractedData.processResult(context, favoriteItem, startTime)
                        } catch (e: Exception) {
                            Log.e(TAG, "解析豆瓣播客数据失败: ${e.message}", e)

                            // 显示错误消息
                            CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                            Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                        }
                    } else {
                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "获取豆瓣内容失败，请重试")
                        Log.e(TAG, "提取豆瓣播客信息失败: 结果为空")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                    }
                }
            )
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到豆瓣链接")
            CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 豆瓣音乐的收藏
     */
    fun SaveMusic(context: Context, appPackage: String, clipboardContent: String?, favoriteItem: SharePanelItem?, startTime: Long) {
        Log.i(TAG, "【时间日志】开始处理豆瓣音乐: ${getElapsedTimeLog(startTime)}")
        CustomToastHelper.showToast(context, "已保存豆瓣音乐到「${favoriteItem?.name}」")
    }

    /**
     * 豆瓣小组的收藏
     */
    fun SaveGroup(context: Context, appPackage: String, clipboardContent: String?, favoriteItem: SharePanelItem?, startTime: Long) {
        Log.i(TAG, "【时间日志】开始处理豆瓣小组: ${getElapsedTimeLog(startTime)}")
        CustomToastHelper.showToast(context, "已保存豆瓣小组到「${favoriteItem?.name}」")
    }

    /**
     * 从剪贴板内容中提取豆瓣链接
     *
     * @param content 剪贴板内容
     * @return 提取的链接，如果未找到则返回null
     */
    private fun extractDoubanLink(content: String?): String? {
        if (content.isNullOrEmpty()) {
            return null
        }

        // 匹配豆瓣链接的正则表达式
        val doubanLinkPattern = """https?://(?:www\.)?douban\.com/[^\s]+""".toRegex()
        val match = doubanLinkPattern.find(content)
        return match?.value
    }

    /**
     * 获取从开始时间到现在的耗时日志
     *
     * @param startTime 开始时间（毫秒）
     * @return 格式化的耗时日志
     */
    private fun getElapsedTimeLog(startTime: Long): String {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - startTime
        val currentTimeStr = timeFormat.format(Date(currentTime))
        return "当前时间: $currentTimeStr, 耗时: ${elapsedTime}ms"
    }
}
