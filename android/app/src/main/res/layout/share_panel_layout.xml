<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical"
  android:padding="0dp">

  <!-- 内部容器 -->
  <RelativeLayout
    android:id="@+id/share_panel_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/panel_background_rounded"
    android:padding="20dp">

    <!-- 关闭按钮 -->
    <ImageButton
      android:id="@+id/close_button"
      android:layout_width="24dp"
      android:layout_height="24dp"
      android:layout_alignParentTop="true"
      android:layout_alignParentEnd="true"
      android:layout_marginTop="0dp"
      android:layout_marginEnd="0dp"
      android:background="@android:color/transparent"
      android:src="@android:drawable/ic_menu_close_clear_cancel"
      android:tint="#FFFFFF"
      android:scaleType="centerInside"
      android:padding="2dp"
      android:contentDescription="关闭面板" />

    <!-- 主要内容容器 -->
    <LinearLayout
      android:id="@+id/main_content_container"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical">

      <!-- 隐藏的标题，保持ID兼容性 -->
      <TextView
        android:id="@+id/title_text_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone" />

      <!-- 任务进度区域 -->
      <LinearLayout
        android:id="@+id/task_progress_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp"
        android:visibility="gone">

        <!-- 任务进度标题 -->
        <TextView
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="任务进度"
          android:textColor="#FFFFFF"
          android:textSize="14sp"
          android:textStyle="bold"
          android:layout_marginBottom="12dp"
          android:gravity="center_vertical" />

        <!-- 进度步骤容器 -->
        <RelativeLayout
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:paddingHorizontal="20dp">

          <!-- 连接线容器 - 在环的中心高度 -->
          <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:layout_marginTop="30dp">

            <!-- 第一条连接线 -->
            <View
              android:layout_width="0dp"
              android:layout_height="2dp"
              android:layout_weight="1"
              android:layout_marginStart="90dp"
              android:layout_marginEnd="30dp"
              android:background="@drawable/progress_dotted_line" />

            <!-- 第二条连接线 -->
            <View
              android:layout_width="0dp"
              android:layout_height="2dp"
              android:layout_weight="1"
              android:layout_marginStart="30dp"
              android:layout_marginEnd="90dp"
              android:background="@drawable/progress_dotted_line" />
          </LinearLayout>

          <!-- 步骤容器 -->
          <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 步骤1：提取视频 -->
            <LinearLayout
              android:id="@+id/step_extract_video"
              android:layout_width="0dp"
              android:layout_height="wrap_content"
              android:layout_weight="1"
              android:orientation="vertical"
              android:gravity="center">

              <RelativeLayout
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginBottom="4dp">

                <!-- 自定义进度圆环 -->
                <com.xunhe.aishoucang.views.CircularProgressView
                  android:id="@+id/progress_extract_video"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:layout_centerInParent="true" />

                <!-- 百分比文本 -->
                <TextView
                  android:id="@+id/percent_extract_video"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_centerInParent="true"
                  android:text="0%"
                  android:textColor="#FFFFFF"
                  android:textSize="10sp"
                  android:textStyle="bold"
                  android:gravity="center" />
              </RelativeLayout>


              <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="提取视频"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:gravity="center" />
            </LinearLayout>

            <!-- 步骤2：解析文案 -->
            <LinearLayout
              android:id="@+id/step_parse_content"
              android:layout_width="0dp"
              android:layout_height="wrap_content"
              android:layout_weight="1"
              android:orientation="vertical"
              android:gravity="center">

              <RelativeLayout
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginBottom="4dp">

                <!-- 自定义进度圆环 -->
                <com.xunhe.aishoucang.views.CircularProgressView
                  android:id="@+id/progress_parse_content"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:layout_centerInParent="true" />

                <!-- 百分比文本 -->
                <TextView
                  android:id="@+id/percent_parse_content"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_centerInParent="true"
                  android:text="0%"
                  android:textColor="#FFFFFF"
                  android:textSize="10sp"
                  android:textStyle="bold"
                  android:gravity="center" />
              </RelativeLayout>


              <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="解析文案"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:gravity="center" />
            </LinearLayout>

            <!-- 步骤3：解析完成 -->
            <LinearLayout
              android:id="@+id/step_complete"
              android:layout_width="0dp"
              android:layout_height="wrap_content"
              android:layout_weight="1"
              android:orientation="vertical"
              android:gravity="center">

              <RelativeLayout
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginBottom="4dp">

                <!-- 自定义进度圆环 -->
                <com.xunhe.aishoucang.views.CircularProgressView
                  android:id="@+id/progress_complete"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:layout_centerInParent="true" />

                <!-- 百分比文本 -->
                <TextView
                  android:id="@+id/percent_complete"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_centerInParent="true"
                  android:text="0%"
                  android:textColor="#FFFFFF"
                  android:textSize="10sp"
                  android:textStyle="bold"
                  android:gravity="center" />
              </RelativeLayout>


              <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="解析完成"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:gravity="center" />
            </LinearLayout>
          </LinearLayout>
        </RelativeLayout>
      </LinearLayout>

      <!-- 标签切换区域 -->
      <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="0dp">

        <!-- 标签栏 -->
        <LinearLayout
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:orientation="horizontal"
          android:layout_marginBottom="12dp"
          android:layout_marginStart="0dp">

          <!-- AI工具箱标签容器 -->
          <LinearLayout
            android:id="@+id/tab_ai_tools_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="24dp"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackground">

            <!-- AI工具箱标签文字 -->
            <TextView
              android:id="@+id/tab_ai_tools"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:text="AI工具箱"
              android:textColor="#FFFFFF"
              android:textSize="14sp"
              android:textStyle="bold"
              android:paddingHorizontal="4dp"
              android:paddingVertical="8dp" />

            <!-- AI工具箱下划线 -->
            <View
              android:id="@+id/tab_ai_tools_underline"
              android:layout_width="48dp"
              android:layout_height="2dp"
              android:background="#FFFFFF"
              android:layout_gravity="center_horizontal"
              android:visibility="visible" />
          </LinearLayout>

          <!-- 任务队列标签容器 -->
          <LinearLayout
            android:id="@+id/tab_task_queue_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackground">

            <!-- 任务队列标签文字 -->
            <TextView
              android:id="@+id/tab_task_queue"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:text="任务队列"
              android:textColor="#AAAAAA"
              android:textSize="14sp"
              android:paddingHorizontal="4dp"
              android:paddingVertical="8dp" />

            <!-- 任务队列下划线 -->
            <View
              android:id="@+id/tab_task_queue_underline"
              android:layout_width="48dp"
              android:layout_height="2dp"
              android:background="#FFFFFF"
              android:layout_gravity="center_horizontal"
              android:visibility="gone" />
          </LinearLayout>
        </LinearLayout>

        <!-- 内容区域容器 -->
        <FrameLayout
          android:layout_width="match_parent"
          android:layout_height="0dp"
          android:layout_weight="1">

          <!-- AI工具箱内容 -->
          <LinearLayout
            android:id="@+id/ai_tools_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical"
            android:visibility="visible">

            <!-- AI工具箱网格 -->
            <GridLayout
              android:id="@+id/ai_tools_grid"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:columnCount="4"
              android:rowCount="1">

              <!-- 创建笔记 -->
              <LinearLayout
                android:id="@+id/tool_create_note"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true">

                <ImageView
                  android:layout_width="28dp"
                  android:layout_height="28dp"
                  android:src="@android:drawable/ic_menu_edit"
                  android:tint="#FFFFFF"
                  android:layout_marginBottom="4dp" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="创建笔记"
                  android:textColor="#d1d3d6"
                  android:textSize="11sp"
                  android:gravity="center" />
              </LinearLayout>



              <!-- 下载视频 -->
              <LinearLayout
                android:id="@+id/tool_download_video"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true">

                <ImageView
                  android:layout_width="28dp"
                  android:layout_height="28dp"
                  android:src="@android:drawable/stat_sys_download"
                  android:tint="#FFFFFF"
                  android:layout_marginBottom="4dp" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="下载视频"
                  android:textColor="#d1d3d6"
                  android:textSize="11sp"
                  android:gravity="center" />
              </LinearLayout>

              <!-- 快速二创 -->
              <LinearLayout
                android:id="@+id/tool_quick_create"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_columnWeight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:visibility="gone">

                <ImageView
                  android:layout_width="28dp"
                  android:layout_height="28dp"
                  android:src="@android:drawable/btn_star_big_on"
                  android:tint="#FFFFFF"
                  android:layout_marginBottom="4dp" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="快速二创"
                  android:textColor="#d1d3d6"
                  android:textSize="11sp"
                  android:gravity="center" />
              </LinearLayout>
            </GridLayout>

            <!-- 分割线 -->
            <View
              android:layout_width="match_parent"
              android:layout_height="1dp"
              android:background="#33FFFFFF"
              android:layout_marginVertical="16dp" />

            <!-- 快速总结到笔记区域 -->
            <LinearLayout
              android:id="@+id/note_container"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical"
              android:layout_marginBottom="0dp">

              <!-- 快速总结到笔记标题 -->
              <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="快速总结到笔记"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="20dp"
                android:gravity="center_vertical" />

              <!-- 笔记区域下拉刷新布局 -->
              <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/note_swipe_refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="185dp">

                <!-- 笔记网格容器 -->
                <androidx.recyclerview.widget.RecyclerView
                  android:id="@+id/note_recycler_view"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:paddingBottom="16dp"
                  android:clipToPadding="false"
                  android:nestedScrollingEnabled="false" />
              </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

              <!-- 分割线 -->
              <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#33FFFFFF"
                android:layout_marginVertical="16dp" />

              <!-- 选择收藏夹区域 -->
              <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="0dp">

                <!-- 选择收藏夹标题 -->
                <TextView
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:text="选择收藏夹"
                  android:textColor="#FFFFFF"
                  android:textSize="14sp"
                  android:textStyle="bold"
                  android:layout_marginBottom="12dp"
                  android:gravity="center_vertical" />

                <!-- 加载状态视图 -->
                <LinearLayout
                  android:id="@+id/loading_container"
                  android:layout_width="match_parent"
                  android:layout_height="200dp"
                  android:gravity="center"
                  android:orientation="vertical"
                  android:visibility="gone">

                  <ProgressBar
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:indeterminate="true"
                    android:indeterminateTint="#3D7AF5" />

                  <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="正在获取分享内容..."
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:fontFamily="sans-serif" />
                </LinearLayout>

                <!-- 收藏夹列表容器 -->
                <LinearLayout
                  android:id="@+id/share_panel_content"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:orientation="vertical">

                  <!-- 添加下拉刷新布局 -->
                  <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- 分享渠道列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                      android:id="@+id/share_panel_recycler_view"
                      android:layout_width="match_parent"
                      android:layout_height="200dp"
                      android:paddingBottom="16dp"
                      android:clipToPadding="false"
                      android:nestedScrollingEnabled="false" />
                  </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
                </LinearLayout>
              </LinearLayout>
            </LinearLayout>
          </LinearLayout>

          <!-- 任务队列内容 -->
          <LinearLayout
            android:id="@+id/task_queue_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 任务队列列表容器 -->
            <FrameLayout
              android:layout_width="match_parent"
              android:layout_height="match_parent">

              <!-- 下拉刷新容器 -->
              <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/task_queue_swipe_refresh"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!-- 任务队列列表 -->
                <androidx.recyclerview.widget.RecyclerView
                  android:id="@+id/task_queue_recycler_view"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:paddingTop="8dp"
                  android:paddingBottom="8dp"
                  android:clipToPadding="false"
                  android:nestedScrollingEnabled="true" />

              </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

              <!-- 任务队列空状态 -->
              <LinearLayout
                android:id="@+id/task_queue_empty_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                  android:layout_width="48dp"
                  android:layout_height="48dp"
                  android:src="@android:drawable/ic_menu_agenda"
                  android:tint="#666666"
                  android:layout_marginBottom="16dp" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="暂无任务"
                  android:textColor="#AAAAAA"
                  android:textSize="16sp"
                  android:layout_marginBottom="8dp" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="您的任务队列为空"
                  android:textColor="#888888"
                  android:textSize="14sp" />
              </LinearLayout>

              <!-- 任务队列加载状态 -->
              <LinearLayout
                android:id="@+id/task_queue_loading_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ProgressBar
                  android:layout_width="40dp"
                  android:layout_height="40dp"
                  android:indeterminate="true"
                  android:indeterminateTint="#3D7AF5"
                  android:layout_marginBottom="16dp" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="正在加载任务..."
                  android:textColor="#AAAAAA"
                  android:textSize="14sp" />
              </LinearLayout>
            </FrameLayout>
          </LinearLayout>
        </FrameLayout>
      </LinearLayout>

      <!-- 底部按钮容器 - 只在AI工具箱tab显示 -->
      <LinearLayout
        android:id="@+id/bottom_buttons_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="2"
        android:layout_marginTop="16dp"
        android:visibility="visible">

        <!-- 取消按钮 -->
        <Button
          android:id="@+id/cancel_button"
          android:layout_width="0dp"
          android:layout_height="48dp"
          android:layout_weight="1"
          android:layout_marginEnd="8dp"
          android:background="#33FFFFFF"
          android:text="取消"
          android:textColor="#FFFFFF"
          android:textSize="14sp"
          android:fontFamily="sans-serif-medium"
          android:elevation="0dp"
          android:stateListAnimator="@null" />

        <!-- 新建收藏夹按钮 -->
        <Button
          android:id="@+id/new_folder_button"
          android:layout_width="0dp"
          android:layout_height="48dp"
          android:layout_weight="1"
          android:layout_marginStart="8dp"
          android:background="#6B9EFF"
          android:text="新建收藏夹"
          android:textColor="#FFFFFF"
          android:textSize="14sp"
          android:fontFamily="sans-serif-medium"
          android:elevation="0dp"
          android:stateListAnimator="@null" />
      </LinearLayout>
    </LinearLayout>
  </RelativeLayout>
</LinearLayout>